# SECURITY TODO - Lock In Habit Tracker

## Overview
This document tracks all verified security issues from the security audit conducted on January 28, 2025.
Each issue includes severity, location, effort estimate, and implementation steps.

**Total Issues: 14** (1 Critical, 3 High, 7 Medium, 3 Low)
**Estimated Total Effort: 15-20 days**

---

## CRITICAL ISSUES (Fix Immediately)

### [ ] CRITICAL-01: Implement Proper ThriveCart Webhook Verification
**Severity:** Critical | **Effort:** 2-3 hours | **Priority:** P0  
**Location:** `src/controllers/thrivecartController.js:79-87`

**Current Issue:**
- Simple string comparison for webhook secret
- Returns `true` when no secret configured
- No HMAC signature verification
- Vulnerable to replay attacks

**Possible Implementation Steps:**
```javascript
// Replace verifyThriveCartSecret method with:
verifyThriveCartSecret(payload, headers) {
  const webhookSecret = process.env.THRIVECART_WEBHOOK_SECRET;
  
  if (!webhookSecret) {
    logger.error('ThriveCart webhook secret not configured');
    return false; // NEVER skip verification
  }
  
  // ThriveCart should provide signature in headers
  const signature = headers['x-thrivecart-signature'];
  if (!signature) {
    logger.error('Missing ThriveCart signature header');
    return false;
  }
  
  // Compute HMAC-SHA256
  const crypto = require('crypto');
  const expectedSignature = crypto
    .createHmac('sha256', webhookSecret)
    .update(JSON.stringify(payload))
    .digest('hex');
  
  // Use constant-time comparison
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}
```

**Testing:**
- Test with valid and invalid signatures
- Ensure webhooks fail without proper secret
- Verify constant-time comparison works

---

## HIGH PRIORITY ISSUES (Fix Within 1 Week)

### [ ] HIGH-01: Always Verify Webhook Signatures
**Severity:** High | **Effort:** 1 hour | **Priority:** P1  
**Location:** `src/controllers/webhookController.js:118`

**Current Issue:**
- Signature verification only in production
- Development/staging environments vulnerable

**Fix:**
```javascript
// Change line 118 from:
if (process.env.NODE_ENV === 'production' && process.env.TWILIO_AUTH_TOKEN) {

// To:
if (process.env.TWILIO_AUTH_TOKEN) {
  // Always verify when token is configured
```

### [ ] HIGH-02: Remove Hardcoded Secrets
**Severity:** High | **Effort:** 30 minutes | **Priority:** P1  
**Location:** `src/tests/thrivecart-webhook-payloads.js:12`

**Current Issue:**
- Test webhook secret hardcoded: `'FUQ2A97V0Q8A'`

**Fix:**
```javascript
// Replace line 12:
thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now()
```

**Additional Steps:**
- Audit all test files for hardcoded secrets
- Add `.env.test` for test-specific secrets
- Update README with test configuration

### [ ] HIGH-03: Tighten Rate Limiting
**Severity:** High | **Effort:** 2 hours | **Priority:** P1  
**Location:** `src/config/constants.js:30-34`

**Current Issue:**
- 100 requests per 15 minutes too permissive
- No progressive penalties
- Phone number can be spoofed

**Fix:**
```javascript
RATE_LIMIT: {
  WINDOW_MS: 5 * 60 * 1000, // 5 minutes (was 15)
  MAX_REQUESTS: 20, // 20 requests (was 100)
  MESSAGE: 'Too many requests. Please try again later.',
  SKIP_SUCCESSFUL_REQUESTS: false, // Count all requests
  PROGRESSIVE_DELAY: true, // Add progressive penalties
}
```

**Additional Implementation:**
```javascript
// In rateLimiter.js, add progressive penalties:
const progressiveRateLimiter = rateLimit({
  ...baseConfig,
  skipSuccessfulRequests: false,
  handler: async (req, res) => {
    const key = req.body?.From || req.ip;
    const violations = await getViolationCount(key);
    const penaltyMinutes = Math.min(60, violations * 5);
    
    // Apply exponential backoff
    await applyPenalty(key, penaltyMinutes);
    
    res.status(429).json({
      error: `Rate limit exceeded. Try again in ${penaltyMinutes} minutes.`
    });
  }
});
```

---

## MEDIUM PRIORITY ISSUES (Fix Within 1 Month)

### [ ] MEDIUM-01: Implement Comprehensive Input Validation
**Severity:** Medium | **Effort:** 3 hours | **Priority:** P2  
**Location:** `src/middleware/validation.js:25-51`

**Current Issue:**
- Basic SQL keyword filtering
- No XSS protection
- Easy to bypass

**Fix:**
```javascript
// Use a validation library
const validator = require('validator');
const DOMPurify = require('isomorphic-dompurify');

function sanitizeInput(text) {
  if (!text) return '';
  
  // Comprehensive sanitization
  let sanitized = text.trim();
  
  // XSS protection
  sanitized = DOMPurify.sanitize(sanitized, { 
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });
  
  // SQL injection prevention (use parameterized queries instead)
  sanitized = validator.escape(sanitized);
  
  // WhatsApp message validation
  if (validator.contains(sanitized, '<script')) {
    logger.warn('Script injection attempt', { attempt: sanitized.substring(0, 50) });
    return '';
  }
  
  // Length limit
  return sanitized.substring(0, 1000);
}
```

### [ ] MEDIUM-02: Fix PII Logging
**Severity:** Medium | **Effort:** 2 hours | **Priority:** P2  
**Location:** Multiple files, example: `src/controllers/webhookControllerCompliant.js:16`

**Current Issue:**
- Phone numbers partially visible in logs
- Email addresses may be exposed

**Fix:**
```javascript
// Create hashing utility
const crypto = require('crypto');

function hashPII(data, type = 'phone') {
  if (!data) return 'unknown';
  
  const hash = crypto
    .createHash('sha256')
    .update(data + process.env.PII_SALT)
    .digest('hex');
  
  // Return first 8 chars for identification
  return `${type}_${hash.substring(0, 8)}`;
}

// Usage:
phoneHash: hashPII(phone, 'phone'),
emailHash: hashPII(email, 'email'),
```

### [ ] MEDIUM-03: Enhance Session Security
**Severity:** Medium | **Effort:** 4 hours | **Priority:** P2  
**Location:** `src/services/sessionManager.js`

**Current Issue:**
- No secure session tokens
- No rotation policy
- 30-minute timeout too long

**Implementation:**
```javascript
class SecureSessionManager {
  constructor() {
    this.sessions = new Map();
    this.SESSION_TIMEOUT = 15 * 60 * 1000; // 15 minutes
    this.TOKEN_ROTATION = 5 * 60 * 1000; // Rotate every 5 minutes
  }
  
  generateSecureToken() {
    return crypto.randomBytes(32).toString('hex');
  }
  
  createSession(userId) {
    const token = this.generateSecureToken();
    const session = {
      token,
      userId,
      createdAt: Date.now(),
      lastActivity: Date.now(),
      rotationDue: Date.now() + this.TOKEN_ROTATION
    };
    
    this.sessions.set(token, session);
    return token;
  }
  
  validateAndRotate(token) {
    const session = this.sessions.get(token);
    if (!session) return null;
    
    // Check timeout
    if (Date.now() - session.lastActivity > this.SESSION_TIMEOUT) {
      this.sessions.delete(token);
      return null;
    }
    
    // Rotate token if due
    if (Date.now() > session.rotationDue) {
      const newToken = this.generateSecureToken();
      this.sessions.delete(token);
      session.token = newToken;
      session.rotationDue = Date.now() + this.TOKEN_ROTATION;
      this.sessions.set(newToken, session);
      return { session, newToken };
    }
    
    session.lastActivity = Date.now();
    return { session, newToken: null };
  }
}
```

### [ ] MEDIUM-04: Update Dependencies
**Severity:** Medium | **Effort:** 2 hours | **Priority:** P2  
**Location:** `package.json`

**Steps:**
```bash
# Audit dependencies
npm audit

# Update with audit fix
npm audit fix

# Check for major updates
npm outdated

# Update carefully, testing after each
npm update [package-name]
```

**Setup Automated Scanning:**
```json
// package.json scripts
"scripts": {
  "security:check": "npm audit",
  "security:fix": "npm audit fix",
  "security:scan": "snyk test"
}
```

### [ ] MEDIUM-05: Improve Error Handling
**Severity:** Medium | **Effort:** 3 hours | **Priority:** P2  
**Location:** `src/server.js:227-239`

**Current Issue:**
- Error messages may leak information
- Stack traces in development

**Fix:**
```javascript
// Create error handler
class SafeErrorHandler {
  static handle(err, req, res, next) {
    // Log full error internally
    logger.error('Application error', {
      error: err.message,
      stack: err.stack,
      url: req.url,
      method: req.method,
      ip: req.ip
    });
    
    // Send safe response
    const isDev = process.env.NODE_ENV === 'development';
    
    res.status(err.status || 500).json({
      error: isDev ? err.message : 'An error occurred',
      code: err.code || 'INTERNAL_ERROR',
      requestId: req.id, // For support reference
      ...(isDev && { stack: err.stack })
    });
  }
}
```

### [ ] MEDIUM-06: Configure Security Headers
**Severity:** Medium | **Effort:** 1 hour | **Priority:** P2  
**Location:** `src/server.js:23`

**Current Issue:**
- Basic Helmet configuration
- No CORS policy
- Missing CSP

**Fix:**
```javascript
const helmet = require('helmet');
const cors = require('cors');

// Configure Helmet with strict settings
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Configure CORS
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || false,
  credentials: true,
  optionsSuccessStatus: 200
}));

// Additional security headers
app.use((req, res, next) => {
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  next();
});
```

### [ ] MEDIUM-07: Standardize SQL Query Patterns
**Severity:** Medium | **Effort:** 3 hours | **Priority:** P3  
**Location:** `src/services/enhancedRetentionService.js:142` and others

**Current Issue:**
- Template literals with INTERVAL syntax
- Inconsistent with parameterized queries
- Could lead to bad patterns

**Fix:**
```javascript
// Option 1: Use make_interval function
const sessionCleanup = await client.query(`
  DELETE FROM user_sessions 
  WHERE last_active < NOW() - make_interval(days => $1)
`, [retentionDays]);

// Option 2: Create safe interval builder
function buildSafeInterval(value, unit) {
  const validUnits = ['day', 'hour', 'minute', 'second'];
  const validValue = parseInt(value, 10);
  
  if (!validUnits.includes(unit) || isNaN(validValue)) {
    throw new Error('Invalid interval parameters');
  }
  
  return `${validValue} ${unit}${validValue !== 1 ? 's' : ''}`;
}

// Usage:
const interval = buildSafeInterval(30, 'day');
const query = `DELETE FROM logs WHERE created < NOW() - INTERVAL '${interval}'`;
```

---

## LOW PRIORITY ISSUES (Fix Within 3 Months)

### [ ] LOW-01: Separate Test Data
**Severity:** Low | **Effort:** 4 hours | **Priority:** P3  
**Location:** Test endpoints in `src/server.js:135-224`

**Fix:**
- Create separate test database
- Use database transactions for test data
- Implement automatic cleanup
- Add test data flags

### [ ] LOW-02: Enhance Database Security
**Severity:** Low | **Effort:** 2 hours | **Priority:** P3  
**Location:** `src/db/connection.js`

**Fix:**
```javascript
ssl: process.env.NODE_ENV === 'production' 
  ? {
      rejectUnauthorized: true,
      ca: fs.readFileSync(process.env.DB_CA_CERT),
      key: fs.readFileSync(process.env.DB_CLIENT_KEY),
      cert: fs.readFileSync(process.env.DB_CLIENT_CERT),
    }
  : false
```

### [ ] LOW-03: Increase Code Entropy
**Severity:** Low | **Effort:** 1 hour | **Priority:** P3  
**Location:** `src/utils/codeGenerator.js`

**Fix:**
```javascript
function generateSecureCode() {
  // Increase entropy
  const randomBytes = crypto.randomBytes(6); // Was 3
  const code = randomBytes.toString('base64')
    .replace(/[+/=]/g, '') // Remove special chars
    .substring(0, 6)
    .toUpperCase();
  
  // Check for collisions
  const exists = await checkCodeExists(code);
  if (exists) {
    return generateSecureCode(); // Recursive retry
  }
  
  return code;
}
```

---

## Implementation Order

### Week 1 (Critical & High)
1. [ ] CRITICAL-01: ThriveCart webhook verification (Day 1)
2. [ ] HIGH-01: Always verify signatures (Day 1)
3. [ ] HIGH-02: Remove hardcoded secrets (Day 1)
4. [ ] HIGH-03: Tighten rate limiting (Day 2-3)

### Week 2-3 (Medium Priority)
5. [ ] MEDIUM-06: Security headers (Day 4)
6. [ ] MEDIUM-04: Update dependencies (Day 5)
7. [ ] MEDIUM-01: Input validation (Day 6-7)
8. [ ] MEDIUM-02: Fix PII logging (Day 8)
9. [ ] MEDIUM-03: Session security (Day 9-10)
10. [ ] MEDIUM-05: Error handling (Day 11)

### Week 4 (Medium/Low)
11. [ ] MEDIUM-07: SQL patterns (Day 12-13)
12. [ ] LOW-03: Code entropy (Day 14)
13. [ ] LOW-02: Database security (Day 15)

### Month 2-3 (Low Priority)
14. [ ] LOW-01: Test data separation

---

## Testing Checklist

### After Each Fix:
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Security test added for the fix
- [ ] No regressions in existing functionality
- [ ] Performance impact measured

### Before Deployment:
- [ ] Run full security scan
- [ ] Penetration testing on critical fixes
- [ ] Load testing with new rate limits
- [ ] Audit logs working correctly
- [ ] Monitoring alerts configured

---

## Monitoring Setup

### Alerts to Configure:
```javascript
// Add to monitoring service
const securityAlerts = {
  webhookVerificationFailure: {
    threshold: 5,
    window: '5m',
    severity: 'critical'
  },
  rateLimitViolation: {
    threshold: 20,
    window: '10m',
    severity: 'warning'
  },
  sqlInjectionAttempt: {
    threshold: 1,
    window: '1m',
    severity: 'critical'
  },
  authenticationFailure: {
    threshold: 10,
    window: '5m',
    severity: 'high'
  }
};
```

---

## Notes

- **Admin Authentication**: The audit incorrectly identified HIGH-04. Admin endpoints already have JWT/API key authentication via AuthMiddleware.
- **SQL Injection**: While MEDIUM-07 mentions template literals, these use configuration constants, not user input. Still worth fixing for consistency.
- **Progressive Approach**: Start with critical issues that could lead to immediate compromise, then work through high and medium priority items.
- **Testing**: Each fix should include corresponding security tests to prevent regression.

---

**Last Updated:** January 28, 2025  
**Next Security Review:** April 2025 (after critical fixes)  
**Contact:** Security Team