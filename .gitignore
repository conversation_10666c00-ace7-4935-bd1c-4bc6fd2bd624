# Dependencies
node_modules/
package-lock.json

# Package managers (using npm, ignore others)
yarn.lock
pnpm-lock.yaml
.pnp.*
.yarn/

# Environment variables
.env
.env.*
*.env
.env.local
.env.*.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Node.js runtime
*.pid
*.seed
*.pid.lock
pids/
lib-cov/

# Testing
coverage/
.nyc_output
*.lcov
.grunt/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# Build
dist/
build/

# Security & Sensitive Data
*.key
*.pem
*.cert
*.crt
.ssh/

# Docker
docker-compose.override.yml

# Database
*.sql.backup

# Bloat Prevention - Files that should not be in root
/test-*.js
/debug-*.js
/send-*.js
/monitor-*.js
/*.html
!public/legal/*.html

# Backup files
*.backup
*.backup-*
*_backup_*
backup-temp.*

# Temporary directories
/temp/
/tmp/

# Debug artifacts
*.html
!public/legal/*.html
!src/**/*.html
debug-*.log
intercepted-*
latest-email.html
customer-*-generated.html


# Claude configuration (private - not for repository)
CLAUDE.md

# Runtime & Cache
.npm/
.eslintcache
.node_repl_history
.cache/

# TypeScript
*.tsbuildinfo
*.js.map

# Claude Flow generated files
.claude/settings.local.json
.mcp.json
claude-flow.config.json
.swarm/
.hive-mind/
memory/claude-flow-data.json
memory/sessions/*
!memory/sessions/README.md
memory/agents/*
!memory/agents/README.md
coordination/memory_bank/*
coordination/subtasks/*
coordination/orchestration/*
*.db
*.db-journal
*.db-wal
*.sqlite
*.sqlite-journal
*.sqlite-wal
claude-flow
claude-flow.bat
claude-flow.ps1
hive-mind-prompt-*.txt
