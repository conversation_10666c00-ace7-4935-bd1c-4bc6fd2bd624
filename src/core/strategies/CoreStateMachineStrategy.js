/**
 * CoreStateMachineStrategy - Main state machine implementation
 * Handles core conversation flow and state transitions
 */

const BaseStrategy = require('./BaseStrategy');
const User = require('../../models/User');
const Habit = require('../../models/Habit');
const AuditLog = require('../../models/AuditLog');
const { STATES, USER_STATUS, AUDIT_EVENTS, HABITS } = require('../../config/constants');
const logger = require('../../config/logger');
const moment = require('moment-timezone');
const motivationalQuotes = require('../../utils/motivationalQuotes');

class CoreStateMachineStrategy extends BaseStrategy {
  constructor(options = {}) {
    super(options);
    
    // Bind all state handlers
    this.transitions = {
      [STATES.MAIN_MENU]: this.handleMainMenu.bind(this),
      [STATES.AWAITING_NAME]: this.handleName.bind(this),
      [STATES.AWAITING_TIMEZONE]: this.handleTimezone.bind(this),
      [STATES.ONBOARDING_MENU]: this.handleOnboardingMenu.bind(this),
      [STATES.SETTING_HABIT]: this.handleSettingHabit.bind(this),
      [STATES.LOGGING_HABITS]: this.handleLoggingHabits.bind(this),
      [STATES.VIEWING_PROGRESS]: this.handleViewingProgress.bind(this),
      [STATES.STATS_MENU]: this.handleStatsMenu.bind(this),
      [STATES.STATS_30_DAY]: this.handleStats30Day.bind(this),
      [STATES.STATS_100_DAY]: this.handleStats100Day.bind(this),
      [STATES.SETTINGS_MENU]: this.handleSettingsMenu.bind(this),
      [STATES.COMPLETION_SCREEN]: this.handleCompletionScreen.bind(this)
    };
  }

  async processMessage(user, message) {
    try {
      // Update last active
      await User.updateLastActive(user.id);

      // Check for test commands in development
      if (process.env.NODE_ENV === 'development' && message.trim().toUpperCase() === 'RESET_TEST') {
        return await this.handleResetTest(user);
      }

      // Get the handler for current state
      const handler = this.transitions[user.current_state] || this.handleMainMenu.bind(this);
      const response = await handler(user, message);

      return response;
    } catch (error) {
      this.logError('State machine error', {
        userId: user.id,
        state: user.current_state,
        error: error.message
      });
      
      await this.auditActivity(user.id, AUDIT_EVENTS.ERROR, {
        state: user.current_state,
        message: 'State processing error'
      });

      return {
        message: "Sorry, something went wrong. Please try again or type 'menu' to return to the main menu.",
        media: null
      };
    }
  }

  // Import the actual state handler methods from the original stateMachine.js
  // These would be copied from the original implementation
  async handleMainMenu(user, message) {
    // Implementation from original stateMachine.js
    const choice = message.trim();
    
    if (choice === '1') {
      await User.updateState(user.id, STATES.LOGGING_HABITS);
      const habits = await Habit.getByUserId(user.id);
      
      if (!habits || habits.length === 0) {
        await User.updateState(user.id, STATES.MAIN_MENU);
        return {
          message: "You haven't set up any habits yet! Please select option 2 to set up your habits first.",
          media: null
        };
      }

      return await this.transitions[STATES.LOGGING_HABITS](user, null);
    } else if (choice === '2') {
      await User.updateState(user.id, STATES.SETTING_HABIT);
      return {
        message: "Let's set up your habits! You can track up to 5 habits.\n\nWhat's your first habit? (e.g., 'Drink 8 glasses of water', 'Exercise for 30 minutes')",
        media: null
      };
    } else if (choice === '3') {
      await User.updateState(user.id, STATES.VIEWING_PROGRESS);
      return await this.transitions[STATES.VIEWING_PROGRESS](user, null);
    } else if (choice === '4') {
      await User.updateState(user.id, STATES.STATS_MENU);
      return await this.transitions[STATES.STATS_MENU](user, null);
    } else if (choice === '5') {
      await User.updateState(user.id, STATES.SETTINGS_MENU);
      return await this.transitions[STATES.SETTINGS_MENU](user, null);
    } else {
      return {
        message: "Please select a valid option (1-5):\n\n📱 *MAIN MENU*\n\n1️⃣ Log Today's Habits\n2️⃣ Set/Edit Habits\n3️⃣ View Progress\n4️⃣ Stats & Insights\n5️⃣ Settings\n\nReply with a number (1-5)",
        media: null
      };
    }
  }

  async handleName(user, message) {
    const name = message.trim();
    
    if (name.length < 1 || name.length > 50) {
      return {
        message: "Please enter a name between 1 and 50 characters:",
        media: null
      };
    }

    await User.updateDisplayName(user.id, name);
    await User.updateState(user.id, STATES.AWAITING_TIMEZONE);
    
    return {
      message: `Nice to meet you, ${name}! 👋\n\nWhat's your timezone? Please enter it in format like:\n• America/New_York\n• Europe/London\n• Asia/Tokyo\n\n(Type 'list' to see all available timezones)`,
      media: null
    };
  }

  async handleTimezone(user, message) {
    const input = message.trim();
    
    if (input.toLowerCase() === 'list') {
      const timezones = moment.tz.names().slice(0, 20);
      return {
        message: `Here are some common timezones:\n\n${timezones.join('\n')}\n\n(Full list at: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones)\n\nPlease enter your timezone:`,
        media: null
      };
    }

    if (!moment.tz.zone(input)) {
      return {
        message: "That doesn't appear to be a valid timezone. Please try again or type 'list' to see options:",
        media: null
      };
    }

    await User.updateTimezone(user.id, input);
    await User.updateStatus(user.id, USER_STATUS.ACTIVE);
    await User.updateState(user.id, STATES.ONBOARDING_MENU);
    
    return {
      message: `Great! Your timezone is set to ${input}.\n\n🎉 *WELCOME TO LOCK IN!* 🎉\n\n*Your 5-habit tracking system*\n\n1️⃣ Set up to 5 daily habits\n2️⃣ Log them each day\n3️⃣ Track your progress\n4️⃣ Build consistency\n\nReady to set up your first habit?\n\n*Reply:*\n1 - Yes, let's go!\n2 - Tell me more first`,
      media: null
    };
  }

  async handleOnboardingMenu(user, message) {
    const choice = message.trim();
    
    if (choice === '1') {
      await User.updateState(user.id, STATES.SETTING_HABIT);
      return {
        message: "Let's set up your habits! You can track up to 5 habits.\n\nWhat's your first habit? (e.g., 'Drink 8 glasses of water', 'Exercise for 30 minutes')",
        media: null
      };
    } else if (choice === '2') {
      return {
        message: "*HOW IT WORKS:*\n\n📝 *Set Your Habits*\nChoose up to 5 daily habits you want to build\n\n✅ *Daily Check-ins*\nEach day, mark which habits you completed\n\n📊 *Track Progress*\nSee your completion %, streaks, and patterns\n\n🎯 *Build Consistency*\nFocus on showing up every day, even if imperfect\n\n*Ready to start?*\nReply '1' to set up your first habit!",
        media: null
      };
    } else {
      return {
        message: "Please choose:\n\n1 - Set up my first habit\n2 - Learn more about Lock In\n\nReply with 1 or 2:",
        media: null
      };
    }
  }

  async handleSettingHabit(user, message) {
    // Implementation would continue here...
    // This is a simplified version - full implementation would be copied from original
    const habitName = message.trim();
    
    if (habitName.length < 3 || habitName.length > 100) {
      return {
        message: "Please enter a habit name between 3 and 100 characters:",
        media: null
      };
    }

    const existingHabits = await Habit.getByUserId(user.id);
    
    if (existingHabits.length >= 5) {
      await User.updateState(user.id, STATES.MAIN_MENU);
      return {
        message: "You've already set up 5 habits (the maximum). You can edit them from the Settings menu.",
        media: null
      };
    }

    await Habit.create(user.id, habitName);
    
    if (existingHabits.length + 1 < 5) {
      return {
        message: `Great! "${habitName}" has been added.\n\nYou have ${4 - existingHabits.length} habit slot(s) remaining.\n\nWould you like to add another habit?\n\n1 - Yes, add another\n2 - No, I'm done`,
        media: null
      };
    } else {
      await User.updateState(user.id, STATES.MAIN_MENU);
      return {
        message: `Perfect! You've set up all 5 habits.\n\nYour habits are ready to track! 🎯\n\n📱 *MAIN MENU*\n\n1️⃣ Log Today's Habits\n2️⃣ Set/Edit Habits\n3️⃣ View Progress\n4️⃣ Stats & Insights\n5️⃣ Settings\n\nReply with a number (1-5)`,
        media: null
      };
    }
  }

  async handleLoggingHabits(user, message) {
    // Simplified implementation - full version would be copied
    const habits = await Habit.getByUserId(user.id);
    
    if (!message) {
      // Initial prompt
      let prompt = "📝 *LOG TODAY'S HABITS*\n\n";
      habits.forEach((habit, index) => {
        prompt += `${index + 1}. ${habit.habit_name}\n`;
      });
      prompt += "\nWhich habits did you complete today?\n\nReply with the numbers (e.g., '1 3 5' or 'all' or 'none')";
      
      return { message: prompt, media: null };
    }

    // Process response
    const input = message.trim().toLowerCase();
    const today = moment().tz(user.timezone).format('YYYY-MM-DD');
    
    if (input === 'all') {
      for (const habit of habits) {
        await Habit.logCompletion(habit.id, user.id, today, true);
      }
      await User.updateState(user.id, STATES.COMPLETION_SCREEN);
      return await this.transitions[STATES.COMPLETION_SCREEN](user, null);
    } else if (input === 'none') {
      for (const habit of habits) {
        await Habit.logCompletion(habit.id, user.id, today, false);
      }
      await User.updateState(user.id, STATES.MAIN_MENU);
      return {
        message: "That's okay! Tomorrow is a fresh start. 💪\n\n" + await this.getMainMenuText(),
        media: null
      };
    } else {
      // Parse numbers
      const numbers = input.split(/[\s,]+/).map(n => parseInt(n)).filter(n => !isNaN(n) && n >= 1 && n <= habits.length);
      
      if (numbers.length === 0) {
        return {
          message: "Please enter valid habit numbers (e.g., '1 3 5'), 'all', or 'none':",
          media: null
        };
      }

      for (let i = 0; i < habits.length; i++) {
        const completed = numbers.includes(i + 1);
        await Habit.logCompletion(habits[i].id, user.id, today, completed);
      }

      await User.updateState(user.id, STATES.COMPLETION_SCREEN);
      return await this.transitions[STATES.COMPLETION_SCREEN](user, null);
    }
  }

  async handleViewingProgress(user, message) {
    // Simplified implementation
    const progress = await Habit.getUserProgress(user.id);
    
    let response = "📊 *YOUR PROGRESS*\n\n";
    
    if (!progress || progress.habits.length === 0) {
      response += "No habits set up yet. Set up habits from the main menu to start tracking!";
    } else {
      response += `*Overall Completion:* ${progress.overall}%\n`;
      response += `*Current Streak:* ${progress.currentStreak} days\n`;
      response += `*Best Streak:* ${progress.bestStreak} days\n\n`;
      
      response += "*Habit Performance:*\n";
      progress.habits.forEach(habit => {
        response += `• ${habit.name}: ${habit.completion}% (${habit.completedDays}/${habit.totalDays} days)\n`;
      });
    }

    await User.updateState(user.id, STATES.MAIN_MENU);
    response += "\n\n" + await this.getMainMenuText();
    
    return { message: response, media: null };
  }

  async handleStatsMenu(user, message) {
    // Implementation continues...
    return {
      message: "📊 *STATS & INSIGHTS*\n\n1️⃣ 30-Day Overview\n2️⃣ 100-Day Challenge Progress\n3️⃣ Back to Main Menu\n\nReply with a number (1-3):",
      media: null
    };
  }

  async handleStats30Day(user, message) {
    // Implementation continues...
    const stats = await Habit.get30DayStats(user.id);
    
    let response = "📊 *30-DAY OVERVIEW*\n\n";
    response += `Completion Rate: ${stats.completionRate}%\n`;
    response += `Perfect Days: ${stats.perfectDays}\n`;
    response += `Current Streak: ${stats.currentStreak} days\n\n`;
    
    await User.updateState(user.id, STATES.STATS_MENU);
    return { message: response + "\n\nReturning to Stats Menu...", media: null };
  }

  async handleStats100Day(user, message) {
    // Implementation continues...
    const stats = await Habit.get100DayStats(user.id);
    
    let response = "💯 *100-DAY CHALLENGE*\n\n";
    response += `Days Completed: ${stats.daysCompleted}/100\n`;
    response += `Progress: ${stats.progressPercentage}%\n`;
    response += `Estimated Completion: ${stats.estimatedCompletion}\n\n`;
    
    await User.updateState(user.id, STATES.STATS_MENU);
    return { message: response + "\n\nReturning to Stats Menu...", media: null };
  }

  async handleSettingsMenu(user, message) {
    const choice = message.trim();
    
    if (choice === '1') {
      // Edit habits
      await User.updateState(user.id, STATES.SETTING_HABIT);
      return {
        message: "Enter the name of a new habit to add (or type 'back' to return):",
        media: null
      };
    } else if (choice === '2') {
      // Change timezone
      await User.updateState(user.id, STATES.AWAITING_TIMEZONE);
      return {
        message: `Your current timezone is: ${user.timezone}\n\nEnter a new timezone (or type 'back' to return):`,
        media: null
      };
    } else if (choice === '3') {
      // Back to main menu
      await User.updateState(user.id, STATES.MAIN_MENU);
      return {
        message: await this.getMainMenuText(),
        media: null
      };
    } else {
      return {
        message: "⚙️ *SETTINGS*\n\n1️⃣ Edit Habits\n2️⃣ Change Timezone\n3️⃣ Back to Main Menu\n\nReply with a number (1-3):",
        media: null
      };
    }
  }

  async handleCompletionScreen(user, message) {
    const today = moment().tz(user.timezone).format('YYYY-MM-DD');
    const logs = await Habit.getTodaysLogs(user.id, today);
    const completedCount = logs.filter(l => l.completed).length;
    const totalCount = logs.length;
    
    const quote = motivationalQuotes.getRandomQuote();
    
    let response = "✅ *HABITS LOGGED!*\n\n";
    response += `You completed ${completedCount}/${totalCount} habits today.\n\n`;
    
    if (completedCount === totalCount) {
      response += "🌟 PERFECT DAY! 🌟\n\n";
    } else if (completedCount >= totalCount * 0.6) {
      response += "💪 Great work! Keep it up!\n\n";
    } else {
      response += "🌱 Every day is a chance to grow!\n\n";
    }
    
    response += `💭 _${quote}_\n\n`;
    
    await User.updateState(user.id, STATES.MAIN_MENU);
    response += await this.getMainMenuText();
    
    return { message: response, media: null };
  }

  async handleResetTest(user) {
    // Test command for development
    await User.resetToOnboarding(user.id);
    await Habit.deleteAll(user.id);
    
    return {
      message: "🔄 TEST RESET COMPLETE\n\nUser reset to onboarding state. All habits deleted.\n\nType 'start' to begin onboarding again.",
      media: null
    };
  }

  async getMainMenuText() {
    return "📱 *MAIN MENU*\n\n1️⃣ Log Today's Habits\n2️⃣ Set/Edit Habits\n3️⃣ View Progress\n4️⃣ Stats & Insights\n5️⃣ Settings\n\nReply with a number (1-5)";
  }
}

module.exports = CoreStateMachineStrategy;