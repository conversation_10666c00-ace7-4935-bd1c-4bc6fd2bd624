/**
 * BaseStrategy - Abstract base class for state machine strategies
 * Defines the interface that all strategies must implement
 */

const { AUDIT_EVENTS } = require('../../config/constants');
const AuditLog = require('../../models/AuditLog');
const logger = require('../../config/logger');

class BaseStrategy {
  constructor(options = {}) {
    this.options = options;
    this.name = this.constructor.name;
  }

  /**
   * Process user message - must be implemented by subclasses
   * @param {Object} user - User object
   * @param {string} message - User's message
   * @returns {Promise<Object>} Response object
   */
  async processMessage(user, message) {
    throw new Error('processMessage must be implemented by subclass');
  }

  /**
   * Handle pre-processing hooks
   * @param {Object} user - User object
   * @param {string} message - User's message
   * @returns {Promise<boolean>} Whether to continue processing
   */
  async preProcess(user, message) {
    // Default implementation - can be overridden
    return true;
  }

  /**
   * Handle post-processing hooks
   * @param {Object} user - User object
   * @param {Object} response - Response object
   * @returns {Promise<Object>} Modified response
   */
  async postProcess(user, response) {
    // Default implementation - can be overridden
    return response;
  }

  /**
   * Audit user activity
   * @param {number} userId - User ID
   * @param {string} event - Event type
   * @param {Object} data - Event data
   */
  async auditActivity(userId, event, data) {
    try {
      await AuditLog.log(userId, event, {
        ...data,
        strategy: this.name,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Audit logging failed', {
        userId,
        event,
        error: error.message,
        strategy: this.name
      });
    }
  }

  /**
   * Log error with context
   * @param {string} message - Error message
   * @param {Object} context - Error context
   */
  logError(message, context) {
    logger.error(message, {
      ...context,
      strategy: this.name
    });
  }

  /**
   * Check if feature is enabled
   * @param {string} feature - Feature name
   * @returns {boolean} Whether feature is enabled
   */
  isFeatureEnabled(feature) {
    return this.options[feature] === true;
  }
}

module.exports = BaseStrategy;