/**
 * ComplianceStrategy - GDPR and WhatsApp compliance handling
 * Adds compliance features on top of core functionality
 */

const BaseStrategy = require('./BaseStrategy');
const CoreStateMachineStrategy = require('./CoreStateMachineStrategy');
const PaymentEnforcementStrategy = require('./PaymentEnforcementStrategy');
const userRightsService = require('../../services/userRightsService');
const complianceService = require('../../services/complianceService');
const { AUDIT_EVENTS } = require('../../config/constants');
const User = require('../../models/User');

class ComplianceStrategy extends BaseStrategy {
  constructor(options = {}) {
    super(options);
    
    // Choose base strategy based on payment enforcement setting
    if (options.enforcePayment) {
      this.baseStrategy = new PaymentEnforcementStrategy(options);
    } else {
      this.baseStrategy = new CoreStateMachineStrategy(options);
    }
    
    // Compliance keywords
    this.stopKeywords = ['STOP', 'UNSUBSCRIBE', 'OPT OUT', 'QUIT', 'CANCEL'];
    this.startKeywords = ['START', 'OPT IN', 'SUBSCRIBE', 'YES'];
    this.dataRightsKeywords = ['DELETE MY DATA', 'DELETE ACCOUNT', 'REMOVE ME', 'ERASE DATA'];
    this.exportKeywords = ['EXPORT MY DATA', 'DOWNLOAD MY DATA', 'GET MY DATA'];
  }

  async processMessage(user, message) {
    try {
      const upperMessage = message.trim().toUpperCase();
      const normalizedPhone = User.normalizePhone(user.phone);

      // Audit message received (without storing content)
      await this.auditActivity(user.id, AUDIT_EVENTS.MESSAGE_RECEIVED, {
        messageLength: message.length,
        timestamp: new Date().toISOString()
      });

      // Priority 1: Check for STOP commands (WhatsApp Business compliance)
      if (this.isStopCommand(upperMessage)) {
        return await this.handleOptOut(user);
      }

      // Priority 2: Check for START commands
      if (this.isStartCommand(upperMessage)) {
        return await this.handleOptIn(user);
      }

      // Priority 3: Data rights commands (GDPR)
      if (this.isDataDeletionRequest(upperMessage)) {
        return await this.handleDataDeletion(user);
      }

      if (this.isDataExportRequest(upperMessage)) {
        return await this.handleDataExport(user);
      }

      // Priority 4: Privacy help
      if (upperMessage === 'PRIVACY' || upperMessage === 'PRIVACY HELP') {
        return this.getPrivacyHelp();
      }

      // Check if user has opted out
      if (user.opted_out_at) {
        return {
          message: "You have opted out of messages. Reply START to opt back in.",
          media: null
        };
      }

      // Check 24-hour messaging window (WhatsApp Business API)
      const lastActive = new Date(user.last_active);
      const hoursSinceActive = (Date.now() - lastActive) / (1000 * 60 * 60);
      
      if (hoursSinceActive > 24 && !this.isWithinServiceWindow(upperMessage)) {
        await this.auditActivity(user.id, AUDIT_EVENTS.MESSAGE_WINDOW_EXPIRED, {
          hoursSinceActive: Math.round(hoursSinceActive)
        });
        
        return {
          message: "Your 24-hour messaging window has expired. Please reply with a message to reopen the conversation.",
          media: null
        };
      }

      // Update last active for message window
      await User.updateLastActive(user.id);

      // Delegate to base strategy
      return await this.baseStrategy.processMessage(user, message);
    } catch (error) {
      this.logError('Compliance strategy error', {
        userId: user.id,
        error: error.message
      });
      
      return {
        message: "Sorry, there was an error processing your request. Please try again.",
        media: null
      };
    }
  }

  isStopCommand(message) {
    return this.stopKeywords.includes(message);
  }

  isStartCommand(message) {
    return this.startKeywords.includes(message);
  }

  isDataDeletionRequest(message) {
    return this.dataRightsKeywords.some(keyword => message.includes(keyword));
  }

  isDataExportRequest(message) {
    return this.exportKeywords.some(keyword => message.includes(keyword));
  }

  isWithinServiceWindow(message) {
    // Messages that reopen the 24-hour window
    const serviceMessages = ['HELP', 'START', 'MENU', 'HI', 'HELLO'];
    return serviceMessages.includes(message);
  }

  async handleOptOut(user) {
    try {
      await complianceService.handleOptOut(user.id);
      await this.auditActivity(user.id, AUDIT_EVENTS.USER_OPTED_OUT, {
        method: 'keyword',
        timestamp: new Date().toISOString()
      });

      return {
        message: "You've been unsubscribed from Lock In messages. " +
                "Reply START at any time to resubscribe. " +
                "Your data will be retained according to our privacy policy. " +
                "To delete your data, reply DELETE MY DATA.",
        media: null
      };
    } catch (error) {
      this.logError('Opt-out error', { userId: user.id, error: error.message });
      return {
        message: "Error processing opt-out. Please try again or contact support.",
        media: null
      };
    }
  }

  async handleOptIn(user) {
    try {
      if (!user.opted_out_at) {
        return {
          message: "You're already subscribed to Lock In messages! Type 'menu' to see options.",
          media: null
        };
      }

      await complianceService.handleOptIn(user.id);
      await this.auditActivity(user.id, AUDIT_EVENTS.USER_OPTED_IN, {
        method: 'keyword',
        timestamp: new Date().toISOString()
      });

      return {
        message: "Welcome back! 🎉\n\n" +
                "You've been resubscribed to Lock In messages.\n\n" +
                "Type 'menu' to see your options.",
        media: null
      };
    } catch (error) {
      this.logError('Opt-in error', { userId: user.id, error: error.message });
      return {
        message: "Error processing opt-in. Please try again or contact support.",
        media: null
      };
    }
  }

  async handleDataDeletion(user) {
    try {
      // Generate verification code
      const verificationCode = Math.random().toString(36).substring(2, 8).toUpperCase();
      
      // Store verification code in session context
      await User.updateSessionContext(user.id, {
        deletionVerificationCode: verificationCode,
        deletionRequestTime: Date.now()
      });

      await this.auditActivity(user.id, AUDIT_EVENTS.DATA_DELETION_REQUESTED, {
        timestamp: new Date().toISOString()
      });

      return {
        message: "⚠️ *DATA DELETION REQUEST*\n\n" +
                "This will permanently delete:\n" +
                "• Your account and profile\n" +
                "• All habit data and logs\n" +
                "• All progress history\n\n" +
                "This action CANNOT be undone.\n\n" +
                `To confirm deletion, reply with: DELETE ${verificationCode}\n\n` +
                "To cancel, reply with anything else.",
        media: null
      };
    } catch (error) {
      this.logError('Data deletion request error', { userId: user.id, error: error.message });
      return {
        message: "Error processing deletion request. Please contact support.",
        media: null
      };
    }
  }

  async handleDataExport(user) {
    try {
      const exportData = await userRightsService.exportUserData(user.id);
      
      await this.auditActivity(user.id, AUDIT_EVENTS.DATA_EXPORTED, {
        timestamp: new Date().toISOString()
      });

      // In production, this would send an email with the data
      // For now, we'll provide a summary
      return {
        message: "📊 *DATA EXPORT REQUESTED*\n\n" +
                "Your data export has been generated and includes:\n" +
                `• Profile information\n` +
                `• ${exportData.habits.length} habits\n` +
                `• ${exportData.logs.length} activity logs\n` +
                `• ${exportData.auditLogs.length} audit entries\n\n` +
                "The full export will be sent to your registered email.\n\n" +
                "_If you don't receive it within 24 hours, please contact support._",
        media: null
      };
    } catch (error) {
      this.logError('Data export error', { userId: user.id, error: error.message });
      return {
        message: "Error generating data export. Please try again or contact support.",
        media: null
      };
    }
  }

  getPrivacyHelp() {
    return {
      message: "🔒 *PRIVACY & DATA RIGHTS*\n\n" +
              "*Your Rights:*\n" +
              "• EXPORT MY DATA - Download all your data\n" +
              "• DELETE MY DATA - Permanently delete account\n" +
              "• STOP - Unsubscribe from messages\n" +
              "• START - Resubscribe to messages\n\n" +
              "*Data We Collect:*\n" +
              "• Phone number (for messaging)\n" +
              "• Display name and timezone\n" +
              "• Habit names and completion logs\n" +
              "• Message timestamps (not content)\n\n" +
              "*Data Retention:*\n" +
              "• Active accounts: Unlimited\n" +
              "• Inactive accounts: 90 days\n" +
              "• After deletion: 30 days (for recovery)\n\n" +
              "Privacy Policy: " + (process.env.PRIVACY_URL || "https://yoursite.com/privacy"),
      media: null
    };
  }
}

module.exports = ComplianceStrategy;