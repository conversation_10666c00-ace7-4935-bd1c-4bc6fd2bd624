/**
 * PaymentEnforcementStrategy - Enforces payment before accessing features
 * Decorates the core state machine with payment checks
 */

const BaseStrategy = require('./BaseStrategy');
const CoreStateMachineStrategy = require('./CoreStateMachineStrategy');
const paymentService = require('../../services/paymentService');
const { USER_STATUS, AUDIT_EVENTS } = require('../../config/constants');
const logger = require('../../config/logger');

class PaymentEnforcementStrategy extends BaseStrategy {
  constructor(options = {}) {
    super(options);
    this.coreStrategy = new CoreStateMachineStrategy(options);
    
    // Features that require payment
    this.paidFeatures = [
      'LOGGING_HABITS',
      'VIEWING_PROGRESS',
      'STATS_MENU',
      'STATS_30_DAY',
      'STATS_100_DAY'
    ];
  }

  async processMessage(user, message) {
    try {
      // Check if user is locked and needs payment
      if (user.status === USER_STATUS.LOCKED) {
        return await this.handleLockedUser(user, message);
      }

      // Check if accessing paid features without payment
      if (!user.is_unlocked && this.requiresPayment(user.current_state)) {
        return await this.handlePaymentRequired(user);
      }

      // Delegate to core strategy
      return await this.coreStrategy.processMessage(user, message);
    } catch (error) {
      this.logError('Payment enforcement error', {
        userId: user.id,
        error: error.message
      });
      
      return {
        message: "Sorry, there was an error processing your request. Please try again.",
        media: null
      };
    }
  }

  requiresPayment(state) {
    return this.paidFeatures.includes(state);
  }

  async handleLockedUser(user, message) {
    const upperMessage = message.trim().toUpperCase();
    
    // Check for access code
    if (upperMessage.length === 6 && /^[A-Z0-9]{6}$/.test(upperMessage)) {
      const isValid = await paymentService.validateAccessCode(upperMessage);
      
      if (isValid) {
        await paymentService.unlockUser(user.id, upperMessage);
        await this.auditActivity(user.id, AUDIT_EVENTS.USER_UNLOCKED, {
          method: 'access_code',
          code: upperMessage.substring(0, 3) + '***'
        });
        
        return {
          message: "🎉 *ACCESS GRANTED!*\n\nWelcome to Lock In! Your account is now unlocked.\n\nLet's get you set up with your habits.\n\nType 'start' to begin!",
          media: null
        };
      } else {
        await this.auditActivity(user.id, AUDIT_EVENTS.INVALID_ACCESS_CODE, {
          attemptedCode: upperMessage.substring(0, 3) + '***'
        });
        
        return {
          message: "❌ Invalid access code. Please check your code and try again.\n\nIf you need help, visit: " + process.env.SUPPORT_URL,
          media: null
        };
      }
    }

    // Show locked message
    return {
      message: this.getLockedMessage(),
      media: null
    };
  }

  async handlePaymentRequired(user) {
    await this.auditActivity(user.id, AUDIT_EVENTS.PAYMENT_REQUIRED, {
      attemptedState: user.current_state
    });

    return {
      message: this.getPaymentRequiredMessage(),
      media: null
    };
  }

  getLockedMessage() {
    return `🔒 *ACCOUNT LOCKED*\n\n` +
           `To unlock all features, you need an access code.\n\n` +
           `*Have an access code?*\n` +
           `Enter your 6-character code now.\n\n` +
           `*Need an access code?*\n` +
           `Get instant access at:\n` +
           `${process.env.PAYMENT_URL || 'https://yoursite.com/purchase'}\n\n` +
           `_Access codes are sent immediately after purchase_`;
  }

  getPaymentRequiredMessage() {
    return `🔒 *PREMIUM FEATURE*\n\n` +
           `This feature requires an unlocked account.\n\n` +
           `Get instant access at:\n` +
           `${process.env.PAYMENT_URL || 'https://yoursite.com/purchase'}\n\n` +
           `Or enter your access code if you have one.`;
  }
}

module.exports = PaymentEnforcementStrategy;