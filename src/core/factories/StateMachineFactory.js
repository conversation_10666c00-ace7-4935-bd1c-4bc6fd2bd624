/**
 * StateMachineFactory - Creates the appropriate state machine strategy
 * based on configuration and environment settings
 */

const CoreStateMachineStrategy = require('../strategies/CoreStateMachineStrategy');
const PaymentEnforcementStrategy = require('../strategies/PaymentEnforcementStrategy');
const ComplianceStrategy = require('../strategies/ComplianceStrategy');
const logger = require('../../config/logger');

class StateMachineFactory {
  /**
   * Create a state machine instance based on configuration
   * @param {Object} options - Configuration options
   * @param {boolean} options.enforceCompliance - Enable GDPR/WhatsApp compliance
   * @param {boolean} options.enforcePayment - Enable payment enforcement
   * @param {boolean} options.testMode - Enable test mode features
   * @returns {BaseStrategy} Configured state machine strategy
   */
  static create(options = {}) {
    // Default configuration
    const config = {
      enforceCompliance: process.env.ENABLE_COMPLIANCE !== 'false',
      enforcePayment: process.env.ENABLE_PAYMENT_ENFORCEMENT === 'true',
      testMode: process.env.NODE_ENV === 'development',
      ...options
    };

    logger.info('Creating state machine', {
      compliance: config.enforceCompliance,
      payment: config.enforcePayment,
      testMode: config.testMode
    });

    // Compliance strategy is the top level and includes everything
    if (config.enforceCompliance) {
      return new ComplianceStrategy(config);
    }

    // Payment enforcement without compliance
    if (config.enforcePayment) {
      return new PaymentEnforcementStrategy(config);
    }

    // Basic state machine without compliance or payment
    return new CoreStateMachineStrategy(config);
  }

  /**
   * Create state machine from environment settings
   * @returns {BaseStrategy} Configured state machine strategy
   */
  static createFromEnv() {
    return this.create({
      enforceCompliance: process.env.ENABLE_COMPLIANCE !== 'false',
      enforcePayment: process.env.ENABLE_PAYMENT_ENFORCEMENT === 'true',
      testMode: process.env.NODE_ENV === 'development'
    });
  }

  /**
   * Create a specific strategy type
   * @param {string} type - Strategy type ('core', 'payment', 'compliance')
   * @param {Object} options - Additional options
   * @returns {BaseStrategy} Specified strategy instance
   */
  static createSpecific(type, options = {}) {
    switch (type) {
      case 'core':
        return new CoreStateMachineStrategy(options);
      case 'payment':
        return new PaymentEnforcementStrategy(options);
      case 'compliance':
        return new ComplianceStrategy(options);
      default:
        throw new Error(`Unknown strategy type: ${type}`);
    }
  }
}

module.exports = StateMachineFactory;