-- Clear today's habit logs for testing
-- This will reset all habits to show ⚠️ status (unlogged)

-- Replace with your test user's phone number
DELETE FROM habit_logs 
WHERE user_id = (
    SELECT id FROM users 
    WHERE phone = '+1234567890'  -- Replace with actual phone number
)
AND log_date = CURRENT_DATE;

-- Verify the deletion
SELECT 
    u.phone,
    u.display_name,
    COUNT(hl.id) as today_logs_count
FROM users u
LEFT JOIN habit_logs hl ON u.id = hl.user_id AND hl.log_date = CURRENT_DATE
WHERE u.phone = '+1234567890'  -- Replace with actual phone number
GROUP BY u.id, u.phone, u.display_name;