const crypto = require('crypto');
const logger = require('../config/logger');
const { products, bumps } = require('./thrivecart/productConfig');
const { parseBumpOrders, generateCodesWithBumps } = require('./thrivecart/codeGeneration');
const { 
  identifyProduct,
  handleOrderSuccess,
  handleSubscriptionPayment,
  handleRefund,
  handleSubscriptionCancelled,
  handleSubscriptionResumed,
  logWebhookEvent
} = require('./thrivecart/webhookHandlers');

/**
 * ThriveCart Controller - Main orchestrator for webhook handling
 */
class ThriveCartController {
  constructor() {
    this.testMode = process.env.PAYMENT_TEST_MODE === 'true';
    this.webhookSecret = process.env.THRIVECART_WEBHOOK_SECRET;
    this.products = products;
    this.bumps = bumps;
  }

  /**
   * Main webhook handler for ThriveCart events
   */
  async handleWebhook(req, res) {
    try {
      const payload = req.body;
      
      // Log webhook event
      await logWebhookEvent(payload, req.headers, this.testMode);

      // Verify ThriveCart secret if provided
      if (!this.verifyThriveCartSecret(payload)) {
        logger.error('Invalid or missing ThriveCart secret');
        return res.status(401).json({ error: 'Invalid secret' });
      }

      // Process different event types
      const eventType = payload.event;
      logger.info('Processing ThriveCart webhook', { eventType, testMode: this.testMode });

      switch (eventType) {
        case 'order.success':
        case 'order.purchase':
          await handleOrderSuccess(payload, this.testMode);
          break;
        case 'order.subscription_payment':
        case 'order.rebill_success':
          await handleSubscriptionPayment(payload);
          break;
        case 'order.refund':
          await handleRefund(payload);
          break;
        case 'order.subscription_cancelled':
        case 'order.subscription_paused':
          await handleSubscriptionCancelled(payload);
          break;
        case 'order.subscription_resumed':
          await handleSubscriptionResumed(payload);
          break;
        default:
          logger.info('Unhandled ThriveCart event type', { eventType });
      }

      res.status(200).json({ received: true });
    } catch (error) {
      logger.error('ThriveCart webhook error', { error: error.message, stack: error.stack });
      res.status(500).json({ error: 'Webhook processing failed' });
    }
  }

  /**
   * Verify ThriveCart secret parameter
   */
  verifyThriveCartSecret(payload) {
    const providedSecret = payload.thrivecart_secret;
    
    if (!this.webhookSecret) {
      logger.warn('ThriveCart webhook secret not configured - skipping verification');
      return true;
    }
    
    return providedSecret === this.webhookSecret;
  }

  /**
   * Test endpoint to simulate webhook events
   */
  async testWebhook(req, res) {
    if (!this.testMode) {
      return res.status(403).json({ error: 'Test mode not enabled' });
    }

    try {
      const { email, productType = 'monthly', includeBumps = [] } = req.body;

      // Build test charges array based on bumps
      const charges = [{
        name: this.products[productType].name,
        amount: this.products[productType].price * 100,
        payment_plan_name: this.products[productType].name
      }];
      
      // Add bump charges
      for (const bumpType of includeBumps) {
        if (this.bumps[bumpType]) {
          charges.push({
            name: this.bumps[bumpType].name,
            amount: this.bumps[bumpType].price * 100
          });
        }
      }
      
      // Calculate total
      const total = charges.reduce((sum, charge) => sum + charge.amount, 0);

      const testPayload = {
        event: 'order.success',
        customer: {
          customer_id: `CUST-${Date.now()}`,
          email: email || '<EMAIL>',
          name: 'Test User'
        },
        order: {
          order_id: `TEST-${Date.now()}`,
          total: total,
          total_str: (total / 100).toFixed(2),
          currency: 'USD',
          charges: charges
        },
        product: {
          product_name: this.products[productType].name
        },
        product_name: this.products[productType].name
      };

      // Process as regular webhook
      req.body = testPayload;
      await this.handleWebhook(req, res);
    } catch (error) {
      logger.error('Test webhook error', { error: error.message });
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = new ThriveCartController();