/**
 * Webhook Event Handlers for ThriveCart
 */

const pool = require('../../db/connection');
const logger = require('../../config/logger');
const { products } = require('./productConfig');
const { generateCodesWithBumps, parseBumpOrders, generateAffiliateCode } = require('./codeGeneration');
const { queueWelcomeEmailWithCodes } = require('./emailNotifications');

/**
 * Identify product from webhook payload using product name instead of pricing
 */
function identifyProduct(payload) {
  // Method 1: Try payload.product_name or payload.product.name (test webhooks)
  const productName = (payload.product?.name || payload.product_name || '').toLowerCase();
  
  if (productName.includes('annual') || productName.includes('yearly')) {
    return 'annual';
  } else if (productName.includes('lifetime') || productName.includes('founder')) {
    return 'lifetime';
  } else if (productName.includes('monthly')) {
    return 'monthly';
  } else if (productName.includes('weekly') || productName.includes('7 day')) {
    return 'weekly';
  }
  
  // Method 2: Search through ALL charges for product (not just charges[0] which might be bump)
  if (payload.order?.charges && Array.isArray(payload.order.charges)) {
    for (const charge of payload.order.charges) {
      // Skip bump charges, look for product charges
      if (charge.item_type === 'product' || !charge.item_type) {
        const chargeName = (charge.name || '').toLowerCase();
        const chargePlanName = (charge.payment_plan_name || '').toLowerCase();
        
        // Check charge name and payment plan name
        if (chargeName.includes('annual') || chargeName.includes('yearly') || chargePlanName.includes('annual') || chargePlanName.includes('yearly')) {
          return 'annual';
        } else if (chargeName.includes('lifetime') || chargeName.includes('founder') || chargePlanName.includes('lifetime') || chargePlanName.includes('founder')) {
          return 'lifetime';
        } else if (chargeName.includes('monthly') || chargePlanName.includes('monthly')) {
          return 'monthly';
        } else if (chargeName.includes('weekly') || chargeName.includes('7 day') || chargePlanName.includes('weekly') || chargePlanName.includes('7 day')) {
          return 'weekly';
        }
      }
    }
  }
  
  // Method 3: Check future_charges frequency (recurring subscription indicator)
  const frequency = payload.order?.future_charges?.[0]?.frequency;
  
  if (frequency === 'year') {
    return 'annual';
  } else if (frequency === 'month') {
    return 'monthly';
  } else if (frequency === 'week') {
    return 'weekly';
  }
  
  // Default to monthly if unable to determine
  logger.warn('Unable to identify product type, defaulting to monthly', {
    productName,
    charges: payload.order?.charges?.map(c => ({ name: c.name, item_type: c.item_type, payment_plan_name: c.payment_plan_name })),
    frequency
  });
  return 'monthly';
}

/**
 * Handle successful order (new purchase)
 */
async function handleOrderSuccess(payload, testMode) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Extract customer data
    const customerData = payload.customer || {};
    const customer = {
      email: customerData.email || payload.email || payload.customer_email,
      customer_id: customerData.id || payload.customer_id,
      name: customerData.name || payload.customer_name || `${customerData.first_name || ''} ${customerData.last_name || ''}`.trim()
    };
    
    if (!customer.email) {
      throw new Error('Customer email is required but was not provided in webhook payload');
    }
    
    // Identify the product
    const productType = identifyProduct(payload);
    const productConfig = products[productType];
    
    // Parse bump orders
    const bumps = parseBumpOrders(payload);
    
    // Calculate total amount
    const orderData = payload.order || {};
    let totalAmount = 0;
    if (orderData.total_str) {
      totalAmount = parseFloat(orderData.total_str);
    } else if (orderData.total) {
      totalAmount = parseFloat(orderData.total) / 100;
    } else {
      totalAmount = parseFloat(payload.order_total || payload.total || productConfig.price);
    }
    
    const order = {
      order_id: orderData.id || payload.order_id || payload.transaction_id || `TC_${Date.now()}_${customer.email}`,
      total: totalAmount,
      currency: payload.currency || orderData.currency || 'USD'
    };
    
    // Extract subscription details
    const subscriptionDetails = {
      subscriptionType: productConfig.subscriptionType,
      nextBillingDate: null,
      billingFrequencyDays: productConfig.billingFrequencyDays,
      paymentPlanId: payload.order?.charges?.[0]?.payment_plan_id || null,
      paymentPlanName: productConfig.name,
      expiresAt: null
    };
    
    // Calculate expiration for non-lifetime subscriptions
    if (productConfig.subscriptionType !== 'lifetime') {
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + productConfig.billingFrequencyDays);
      subscriptionDetails.expiresAt = expirationDate;
      subscriptionDetails.nextBillingDate = expirationDate;
    }
    
    // Check if user already exists
    const existingUser = await client.query(
      'SELECT * FROM paid_users WHERE email = $1',
      [customer.email]
    );

    let paidUser;
    let allCodes = [];
    
    if (existingUser.rows.length > 0) {
      // Update existing user
      paidUser = existingUser.rows[0];
      
      // Generate codes with bumps
      allCodes = await generateCodesWithBumps(productType, bumps);
      
      // Update user record
      const updateResult = await client.query(
        `UPDATE paid_users 
         SET status = 'active',
             subscription_type = $1,
             amount_paid = $2,
             next_billing_date = $3,
             billing_frequency_days = $4,
             payment_plan_id = $5,
             payment_plan_name = $6,
             expires_at = $7,
             updated_at = NOW()
         WHERE email = $8
         RETURNING *`,
        [
          subscriptionDetails.subscriptionType,
          order.total,
          subscriptionDetails.nextBillingDate,
          subscriptionDetails.billingFrequencyDays,
          subscriptionDetails.paymentPlanId,
          subscriptionDetails.paymentPlanName,
          subscriptionDetails.expiresAt,
          customer.email
        ]
      );
      paidUser = updateResult.rows[0];
      
    } else {
      // Generate codes with bumps
      allCodes = await generateCodesWithBumps(productType, bumps);
      
      // Get primary code
      const primaryCode = allCodes.find(c => c.type === 'primary').code;
      
      // Generate affiliate code for eligible products
      const affiliateCode = productConfig.isAffiliate ? await generateAffiliateCode() : null;

      // Create new paid user record
      const paidUserResult = await client.query(
        `INSERT INTO paid_users (
          email, access_code, subscription_type, subscription_id,
          customer_id, amount_paid, currency, status,
          is_affiliate, affiliate_code, paid_at, expires_at,
          next_billing_date, billing_frequency_days,
          payment_plan_id, payment_plan_name,
          fastspring_order_id, test_mode
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
        RETURNING *`,
        [
          customer.email,
          primaryCode,
          subscriptionDetails.subscriptionType,
          order.order_id,
          customer.customer_id || customer.email,
          order.total,
          order.currency || 'USD',
          'active',
          productConfig.isAffiliate,
          affiliateCode,
          new Date(),
          subscriptionDetails.expiresAt,
          subscriptionDetails.nextBillingDate,
          subscriptionDetails.billingFrequencyDays,
          subscriptionDetails.paymentPlanId,
          subscriptionDetails.paymentPlanName,
          order.order_id,
          testMode
        ]
      );

      paidUser = paidUserResult.rows[0];
      paidUser.affiliate_code = affiliateCode;
    }

    // Create access code records for all codes
    for (const codeInfo of allCodes) {
      await client.query(
        `INSERT INTO access_codes (code, paid_user_id, is_active, notes)
         VALUES ($1, $2, $3, $4)`,
        [
          codeInfo.code, 
          paidUser.id, 
          true,
          codeInfo.description
        ]
      );
    }

    // Log transaction
    await client.query(
      `INSERT INTO payment_transactions (
        paid_user_id, transaction_id, type, amount, currency,
        fastspring_order_id, transaction_date
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
      [
        paidUser.id,
        order.order_id,
        'payment',
        order.total,
        order.currency || 'USD',
        order.order_id,
        new Date()
      ]
    );

    // Queue welcome email with all codes
    await queueWelcomeEmailWithCodes(paidUser, allCodes, productConfig, bumps);

    // Mark webhook as processed
    await markWebhookProcessed(payload);

    await client.query('COMMIT');
    
    logger.info('Order processed successfully', {
      email: customer.email,
      productType,
      bumps: bumps.length,
      totalCodes: allCodes.length
    });
    
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Error processing ThriveCart order', { error: error.message });
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Handle subscription payment (renewal)
 */
async function handleSubscriptionPayment(payload) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    const customerData = payload.customer || {};
    const customer = {
      email: customerData.email || payload.email || payload.customer_email
    };
    
    if (!customer.email) {
      logger.error('Customer email missing in subscription payment', { payload });
      throw new Error('Customer email is required');
    }
    
    // Identify product type
    const productType = identifyProduct(payload);
    const productConfig = products[productType];
    
    const orderData = payload.order || {};
    let totalAmount = 0;
    if (orderData.total_str) {
      totalAmount = parseFloat(orderData.total_str);
    } else if (orderData.total) {
      totalAmount = parseFloat(orderData.total) / 100;
    } else {
      totalAmount = parseFloat(payload.order_total || payload.total || 0);
    }
    
    const order = {
      order_id: orderData.id || payload.order_id || payload.transaction_id || `TC_${Date.now()}_${customer.email}`,
      total: totalAmount,
      currency: payload.currency || orderData.currency || 'USD'
    };
    
    // Calculate new expiration
    let newExpiresAt = null;
    if (productConfig.subscriptionType !== 'lifetime' && productConfig.billingFrequencyDays) {
      newExpiresAt = new Date();
      newExpiresAt.setDate(newExpiresAt.getDate() + productConfig.billingFrequencyDays);
    }
    
    // Update subscription status and extend expiration
    await client.query(
      `UPDATE paid_users 
       SET status = 'active',
           expires_at = $1,
           next_billing_date = $2,
           updated_at = NOW()
       WHERE email = $3`,
      [newExpiresAt, newExpiresAt, customer.email]
    );

    // Get user ID for transaction logging
    const userResult = await client.query(
      'SELECT id, subscription_type FROM paid_users WHERE email = $1',
      [customer.email]
    );

    if (userResult.rows[0]) {
      // Log transaction
      await client.query(
        `INSERT INTO payment_transactions (
          paid_user_id, transaction_id, type, amount, currency,
          fastspring_order_id, transaction_date, extends_expiration
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
        [
          userResult.rows[0].id,
          order.order_id,
          'payment',
          order.total,
          order.currency || 'USD',
          order.order_id,
          new Date(),
          true
        ]
      );
    }

    await client.query('COMMIT');
    
    logger.info('Subscription payment processed - access extended', {
      email: customer.email,
      amount: order.total,
      newExpiresAt,
      productType
    });
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Error handling subscription payment', { error: error.message });
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Handle refund
 */
async function handleRefund(payload) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    const customerData = payload.customer || {};
    const customer = {
      email: customerData.email || payload.email || payload.customer_email
    };
    
    if (!customer.email) {
      logger.error('Customer email missing in refund', { payload });
      throw new Error('Customer email is required');
    }
    
    const orderData = payload.order || {};
    let totalAmount = 0;
    if (orderData.total_str) {
      totalAmount = parseFloat(orderData.total_str);
    } else if (orderData.total) {
      totalAmount = parseFloat(orderData.total) / 100;
    } else {
      totalAmount = parseFloat(payload.order_total || payload.total || 0);
    }
    
    const order = {
      order_id: orderData.id || payload.order_id || payload.transaction_id || `TC_${Date.now()}_${customer.email}`,
      total: totalAmount,
      currency: payload.currency || orderData.currency || 'USD'
    };
    
    // Update user status
    await client.query(
      `UPDATE paid_users 
       SET status = 'expired',
           updated_at = NOW()
       WHERE email = $1`,
      [customer.email]
    );

    // Get user ID
    const userResult = await client.query(
      'SELECT id FROM paid_users WHERE email = $1',
      [customer.email]
    );

    if (userResult.rows[0]) {
      // Log refund transaction
      await client.query(
        `INSERT INTO payment_transactions (
          paid_user_id, transaction_id, type, amount, currency,
          fastspring_order_id, transaction_date
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          userResult.rows[0].id,
          order.order_id,
          'refund',
          -Math.abs(order.total),
          order.currency || 'USD',
          order.order_id,
          new Date()
        ]
      );

      // Deactivate all access codes for this user
      await client.query(
        `UPDATE access_codes 
         SET is_active = false 
         WHERE paid_user_id = $1`,
        [userResult.rows[0].id]
      );
    }

    await client.query('COMMIT');
    
    logger.info('Refund processed', {
      email: customer.email,
      amount: order.total
    });
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Error handling refund', { error: error.message });
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Handle subscription cancellation
 */
async function handleSubscriptionCancelled(payload) {
  try {
    const customerData = payload.customer || {};
    const customer = {
      email: customerData.email || payload.email || payload.customer_email
    };
    
    if (!customer.email) {
      logger.error('Customer email missing in cancellation', { payload });
      return;
    }
    
    await pool.query(
      `UPDATE paid_users 
       SET status = 'cancelled',
           expires_at = NOW(),
           updated_at = NOW()
       WHERE email = $1`,
      [customer.email]
    );

    logger.info('Subscription cancelled', {
      email: customer.email
    });
  } catch (error) {
    logger.error('Error handling subscription cancellation', { error: error.message });
    throw error;
  }
}

/**
 * Handle subscription resumption
 */
async function handleSubscriptionResumed(payload) {
  try {
    const customerData = payload.customer || {};
    const customer = {
      email: customerData.email || payload.email || payload.customer_email
    };
    
    if (!customer.email) {
      logger.error('Customer email missing in resumption', { payload });
      return;
    }
    
    await pool.query(
      `UPDATE paid_users 
       SET status = 'active',
           expires_at = NULL,
           updated_at = NOW()
       WHERE email = $1`,
      [customer.email]
    );

    logger.info('Subscription resumed', {
      email: customer.email
    });
  } catch (error) {
    logger.error('Error handling subscription resumption', { error: error.message });
    throw error;
  }
}

/**
 * Mark webhook as processed
 */
async function markWebhookProcessed(payload) {
  try {
    const eventId = payload.order?.order_id || payload.event_id;
    await pool.query(
      `UPDATE webhook_events 
       SET processed = true, processed_at = NOW()
       WHERE payload->>'order_id' = $1 OR event_id = $1`,
      [eventId]
    );
  } catch (error) {
    logger.error('Error marking webhook processed', { error: error.message });
  }
}

/**
 * Log webhook event for auditing
 */
async function logWebhookEvent(payload, headers, testMode) {
  try {
    // DEBUG: Identify product BEFORE logging to see detection logic
    const identifiedProduct = identifyProduct(payload);
    const productConfig = products[identifiedProduct];
    
    const debugData = {
      event: payload.event,
      customer_email: payload.customer?.email,
      order_total: payload.order?.total || payload.order?.total_str,
      product_name: payload.product?.name || payload.product_name,
      payment_plan_name: payload.order?.charges?.[0]?.payment_plan_name,
      identified_product: identifiedProduct,
      product_config_name: productConfig?.name,
      product_config_subscription_type: productConfig?.subscriptionType,
      webhook_charges: payload.order?.charges?.map(c => ({
        name: c.name,
        amount: c.amount,
        payment_plan_name: c.payment_plan_name
      })),
      timestamp: new Date().toISOString(),
      test_mode: testMode
    };

    logger.info('ThriveCart webhook received', debugData);

    await pool.query(
      `INSERT INTO webhook_events (event_type, payload, headers, source, test_mode)
       VALUES ($1, $2, $3, $4, $5)`,
      [
        payload.event || 'unknown',
        JSON.stringify(payload),
        JSON.stringify(headers),
        'thrivecart',
        testMode
      ]
    );
  } catch (error) {
    logger.error('Error logging webhook event', { error: error.message });
  }
}

module.exports = {
  identifyProduct,
  handleOrderSuccess,
  handleSubscriptionPayment,
  handleRefund,
  handleSubscriptionCancelled,
  handleSubscriptionResumed,
  logWebhookEvent,
  markWebhookProcessed
};