/**
 * ThriveCart Product and Bump Configuration
 */

const products = {
  'annual': {
    name: 'Annual',
    price: 39.99,
    subscriptionType: 'yearly',
    billingFrequencyDays: 365,
    isAffiliate: true,
    supportsBumps: true
  },
  'monthly': {
    name: 'Monthly',
    price: 5.99,
    subscriptionType: 'monthly',
    billingFrequencyDays: 30,
    isAffiliate: false,
    supportsBumps: false
  },
  'weekly': {
    name: 'Weekly',
    price: 2.99,
    subscriptionType: 'weekly',
    billingFrequencyDays: 7,
    isAffiliate: false,
    supportsBumps: false
  },
  'lifetime': {
    name: 'Lifetime',
    price: 99.99,
    subscriptionType: 'lifetime',
    billingFrequencyDays: null,
    isAffiliate: true,
    supportsBumps: false
  }
};

const bumps = {
  'friend': {
    name: 'Friend Bump',
    price: 20.00,
    additionalCodes: 1,  // 1 extra code = 2 total
    description: 'Get an extra code for a friend'
  },
  'tribe': {
    name: 'Tribe Bump',
    price: 60.00,
    additionalCodes: 3,  // 3 extra codes = 4 total
    description: 'Get 3 extra codes for your tribe'
  }
};

module.exports = {
  products,
  bumps
};