/**
 * Access Code Generation Logic for ThriveCart
 */

const { generateAccessCode, generateAffiliateCode } = require('../../utils/codeGenerator');
const { bumps } = require('./productConfig');
const logger = require('../../config/logger');

/**
 * Generate multiple access codes based on bump orders
 */
async function generateCodesWithBumps(productType, purchaseBumps) {
  const codes = [];
  
  // Generate the main access code
  const mainCode = await generateAccessCode();
  codes.push({
    code: mainCode,
    type: 'primary',
    description: 'Main access code'
  });
  
  // Generate additional codes for bump orders
  for (const bump of purchaseBumps) {
    const bumpConfig = bumps[bump.type];
    if (bumpConfig) {
      for (let i = 0; i < bumpConfig.additionalCodes; i++) {
        const additionalCode = await generateAccessCode();
        codes.push({
          code: additionalCode,
          type: 'bump',
          bumpType: bump.type,
          description: `${bumpConfig.name} - Code ${i + 1}`
        });
      }
    }
  }
  
  return codes;
}

/**
 * Parse bump orders from the charges array
 */
function parseBumpOrders(payload) {
  const parsedBumps = [];
  
  // Check the charges array for bump orders
  if (payload.order?.charges && Array.isArray(payload.order.charges)) {
    for (const charge of payload.order.charges) {
      const chargeName = (charge.name || charge.item_name || '').toLowerCase();
      
      // Check if this is a bump order
      if (chargeName.includes('friend') || chargeName.includes('extra code')) {
        parsedBumps.push({
          type: 'friend',
          price: parseFloat(charge.amount) / 100, // Convert from cents
          quantity: 1
        });
      } else if (chargeName.includes('tribe') || chargeName.includes('multiple codes')) {
        parsedBumps.push({
          type: 'tribe',
          price: parseFloat(charge.amount) / 100,
          quantity: 1
        });
      }
    }
  }
  
  // Alternative: check for bump_orders or upsells in payload
  if (payload.bump_orders && Array.isArray(payload.bump_orders)) {
    for (const bump of payload.bump_orders) {
      const bumpName = (bump.name || '').toLowerCase();
      
      if (bumpName.includes('friend')) {
        parsedBumps.push({
          type: 'friend',
          price: parseFloat(bump.price || 20),
          quantity: bump.quantity || 1
        });
      } else if (bumpName.includes('tribe')) {
        parsedBumps.push({
          type: 'tribe',
          price: parseFloat(bump.price || 60),
          quantity: bump.quantity || 1
        });
      }
    }
  }
  
  return parsedBumps;
}

module.exports = {
  generateCodesWithBumps,
  parseBumpOrders,
  generateAffiliateCode
};