/**
 * Email Notification Logic for ThriveCart
 */

const pool = require('../../db/connection');
const logger = require('../../config/logger');
const emailService = require('../../services/emailService');
const { bumps } = require('./productConfig');

/**
 * Queue welcome email with multiple access codes
 */
async function queueWelcomeEmailWithCodes(paidUser, codes, productConfig, purchaseBumps) {
  try {
    const templateData = {
      // Premium template variables
      subscription_type: productConfig.name,
      subscription_price: productConfig.subscriptionType === 'lifetime' 
        ? 'One-time payment - $99.99' 
        : `$${productConfig.price}/${productConfig.subscriptionType === 'yearly' ? 'year' : productConfig.subscriptionType === 'monthly' ? 'month' : 'week'}`,
      primary_access_code: codes.find(c => c.type === 'primary').code,
      bonus_codes: purchaseBumps.length > 0 ? codes.filter(c => c.type === 'bump').map(c => c.code) : null,
      botPhone: process.env.TWILIO_PHONE_NUMBER || '+19035155547',
      
      // Legacy compatibility (remove when old templates are phased out)
      accessCode: codes.find(c => c.type === 'primary').code,
      accessCodes: codes,
      primaryCode: codes.find(c => c.type === 'primary').code,
      subscriptionType: paidUser.subscription_type,
      subscriptionTypeDisplay: productConfig.name,
      pricingText: productConfig.subscriptionType === 'lifetime' 
        ? 'One-time payment' 
        : `$${productConfig.price}/${productConfig.subscriptionType === 'yearly' ? 'year' : productConfig.subscriptionType === 'monthly' ? 'month' : 'week'}`,
      amountPaid: paidUser.amount_paid,
      affiliateCode: paidUser.affiliate_code,
      isAffiliate: productConfig.isAffiliate,
      isLifetime: productConfig.subscriptionType === 'lifetime',
      hasBumps: purchaseBumps.length > 0,
      bumps: purchaseBumps.map(b => ({
        type: b.type,
        name: bumps[b.type].name,
        codesCount: bumps[b.type].additionalCodes + 1
      }))
    };

    // Log template data for monitoring
    logger.info('Email template data prepared', {
      template: codes.length > 1 ? 'welcome_multi_code' : 'welcome_dynamic',
      subscription_type: templateData.subscription_type,
      email: paidUser.email
    });

    // Use enhanced template for multiple codes
    const template = codes.length > 1 ? 'welcome_multi_code' : 'welcome_dynamic';
    
    await pool.query(
      `INSERT INTO email_queue (to_email, subject, template, template_data, priority)
       VALUES ($1, $2, $3, $4, $5)`,
      [
        paidUser.email,
        'Welcome to LOCK IN - Your Access Codes Are Here!',  // Default subject
        template,
        JSON.stringify(templateData),
        1
      ]
    );

    // Process email queue immediately
    await emailService.processQueue();
  } catch (error) {
    logger.error('Error queueing welcome email', { error: error.message });
  }
}

module.exports = {
  queueWelcomeEmailWithCodes
};