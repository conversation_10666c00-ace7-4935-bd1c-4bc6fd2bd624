const pool = require('../db/connection');
const logger = require('../config/logger');

class AuditLog {
  static async log(userId, eventType, eventData = {}) {
    try {
      // Never log phone numbers or other PII
      const sanitizedData = { ...eventData };
      delete sanitizedData.phone;
      delete sanitizedData.phoneNumber;
      
      await pool.query(
        'INSERT INTO audit_logs (user_id, event_type, event_data, timestamp) VALUES ($1, $2, $3, NOW())',
        [userId, eventType, JSON.stringify(sanitizedData)]
      );
    } catch (error) {
      // Don't throw audit log errors - just log them
      logger.error('Error creating audit log', { error: error.message });
    }
  }

  static async getByUser(userId, limit = 100) {
    try {
      const result = await pool.query(
        'SELECT * FROM audit_logs WHERE user_id = $1 ORDER BY timestamp DESC LIMIT $2',
        [userId, limit]
      );
      return result.rows;
    } catch (error) {
      logger.error('Error getting audit logs', { error: error.message });
      throw error;
    }
  }

  static async cleanup(daysToKeep = 90) {
    try {
      const result = await pool.query(
        'DELETE FROM audit_logs WHERE timestamp < NOW() - INTERVAL \'%s days\' RETURNING id',
        [daysToKeep]
      );
      logger.info(`Cleaned up ${result.rowCount} old audit logs`);
      return result.rowCount;
    } catch (error) {
      logger.error('Error cleaning up audit logs', { error: error.message });
      throw error;
    }
  }
}

module.exports = AuditLog;