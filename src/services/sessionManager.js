const User = require('../models/User');
const AuditLog = require('../models/AuditLog');
const { SESSION_TIMEOUT_MS, STATES, AUDIT_EVENTS } = require('../config/constants');
const logger = require('../config/logger');

class SessionManager {
  constructor() {
    this.checkInterval = null;
  }

  start() {
    // Check for timed-out sessions every minute
    this.checkInterval = setInterval(() => {
      this.cleanupInactiveSessions();
    }, 60000); // 1 minute

    logger.info('Session manager started');
  }

  stop() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
    logger.info('Session manager stopped');
  }

  async cleanupInactiveSessions() {
    try {
      const inactiveUsers = await User.getInactiveSessions(SESSION_TIMEOUT_MS);
      
      for (const user of inactiveUsers) {
        await this.resetUserSession(user);
      }

      if (inactiveUsers.length > 0) {
        logger.info(`Cleaned up ${inactiveUsers.length} inactive sessions`);
      }
    } catch (error) {
      logger.error('Error cleaning up inactive sessions', { error: error.message });
    }
  }

  async resetUserSession(user) {
    try {
      // Reset user to main menu
      await User.updateState(user.id, STATES.MAIN_MENU, {});
      
      // Log the timeout event
      await AuditLog.log(user.id, AUDIT_EVENTS.SESSION_TIMEOUT, {
        previousState: user.current_state,
        lastActive: user.last_active
      });

      logger.debug(`Session timeout for user ${user.id}`);
    } catch (error) {
      logger.error('Error resetting user session', { 
        userId: user.id, 
        error: error.message 
      });
    }
  }

  async validateSession(user) {
    const lastActive = new Date(user.last_active);
    const now = new Date();
    const timeSinceActive = now - lastActive;

    if (timeSinceActive > SESSION_TIMEOUT_MS && user.current_state !== STATES.MAIN_MENU) {
      await this.resetUserSession(user);
      return false;
    }

    return true;
  }
}

module.exports = new SessionManager();