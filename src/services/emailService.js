const emailQueue = require('./email/emailQueue');
const emailTemplates = require('./email/emailTemplates');

/**
 * Email Service - Main entry point for email functionality
 * Delegates to specialized modules for queue processing and templates
 */
class EmailService {
  constructor() {
    // Template references for backward compatibility
    this.templates = {
      welcome_monthly: emailTemplates.getWelcomeMonthlyTemplate.bind(emailTemplates),
      welcome_yearly: emailTemplates.getWelcomeYearlyTemplate.bind(emailTemplates),
      welcome_dynamic: emailTemplates.getWelcomeDynamicTemplate.bind(emailTemplates),
      welcome_multi_code: emailTemplates.getWelcomeMultiCodeTemplate.bind(emailTemplates),
      payment_failed: emailTemplates.getPaymentFailedTemplate.bind(emailTemplates),
      subscription_cancelled: emailTemplates.getSubscriptionCancelledTemplate.bind(emailTemplates)
    };
  }

  /**
   * Process email queue - delegates to emailQueue module
   */
  async processQueue() {
    return emailQueue.processQueue();
  }

  /**
   * Start email queue processor (runs every minute)
   */
  startQueueProcessor() {
    setInterval(() => {
      this.processQueue();
    }, 60000); // Process every minute
    
    // Process immediately on startup
    this.processQueue();
  }

  // Template methods exposed for backward compatibility
  getWelcomeMonthlyTemplate(data) {
    return emailTemplates.getWelcomeMonthlyTemplate(data);
  }

  getWelcomeYearlyTemplate(data) {
    return emailTemplates.getWelcomeYearlyTemplate(data);
  }

  getWelcomeDynamicTemplate(data) {
    return emailTemplates.getWelcomeDynamicTemplate(data);
  }

  getWelcomeMultiCodeTemplate(data) {
    return emailTemplates.getWelcomeMultiCodeTemplate(data);
  }

  getPaymentFailedTemplate(data) {
    return emailTemplates.getPaymentFailedTemplate(data);
  }

  getSubscriptionCancelledTemplate(data) {
    return emailTemplates.getSubscriptionCancelledTemplate(data);
  }
}

module.exports = new EmailService();