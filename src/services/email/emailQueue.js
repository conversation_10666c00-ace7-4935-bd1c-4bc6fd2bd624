const pool = require('../../db/connection');
const logger = require('../../config/logger');
const smtpConfig = require('./smtpConfig');
const emailTemplates = require('./emailTemplates');

class EmailQueue {
  /**
   * Process email queue
   */
  async processQueue() {
    try {
      // Get pending emails
      const result = await pool.query(
        `SELECT * FROM email_queue 
         WHERE status = 'pending' 
         AND retry_count < 3
         ORDER BY priority ASC, created_at ASC 
         LIMIT 10`
      );

      for (const email of result.rows) {
        await this.sendEmail(email);
      }
    } catch (error) {
      logger.error('Error processing email queue', { error: error.message });
    }
  }

  /**
   * Send individual email
   */
  async sendEmail(emailRecord) {
    try {
      // Get template
      const templateFunc = emailTemplates[emailRecord.template];
      if (!templateFunc) {
        throw new Error(`Unknown email template: ${emailRecord.template}`);
      }

      // Generate email content
      // template_data is already a JSON object from PostgreSQL JSONB column
      const templateData = typeof emailRecord.template_data === 'string' 
        ? JSON.parse(emailRecord.template_data) 
        : emailRecord.template_data;
      const { subject, html, text } = templateFunc.call(emailTemplates, templateData);

      // Get transporter
      const transporter = smtpConfig.getTransporter();

      // Send email
      if (transporter) {
        await transporter.sendMail({
          from: `"Lockin Habit Tracker" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
          to: emailRecord.to_email,
          subject: emailRecord.subject || subject,
          html,
          text,
          replyTo: process.env.EMAIL_REPLY_TO || '<EMAIL>'
        });

        // Mark as sent
        await pool.query(
          `UPDATE email_queue 
           SET status = 'sent', sent_at = NOW(), updated_at = NOW()
           WHERE id = $1`,
          [emailRecord.id]
        );

        logger.info('Email sent successfully', {
          to: emailRecord.to_email,
          template: emailRecord.template
        });
      } else {
        // Log email content if transporter not available
        logger.info('Email (logged only - no SMTP configured)', {
          to: emailRecord.to_email,
          subject: emailRecord.subject || subject,
          template: emailRecord.template,
          content: text
        });

        // Mark as sent (logged)
        await pool.query(
          `UPDATE email_queue 
           SET status = 'sent', sent_at = NOW(), updated_at = NOW()
           WHERE id = $1`,
          [emailRecord.id]
        );
      }
    } catch (error) {
      logger.error('Error sending email', {
        error: error.message,
        emailId: emailRecord.id
      });

      // Update retry count
      await pool.query(
        `UPDATE email_queue 
         SET retry_count = retry_count + 1, 
             error_message = $1,
             updated_at = NOW()
         WHERE id = $2`,
        [error.message, emailRecord.id]
      );

      // Mark as failed if max retries reached
      if (emailRecord.retry_count >= 2) {
        await pool.query(
          `UPDATE email_queue 
           SET status = 'failed', updated_at = NOW()
           WHERE id = $1`,
          [emailRecord.id]
        );
      }
    }
  }
}

module.exports = new EmailQueue();