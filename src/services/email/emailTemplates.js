const { getEmailFooterHTML, getEmailFooterText } = require('../../templates/emailFooter');

/**
 * Email template collection for Lock In Habit Tracker
 */
class EmailTemplates {
  /**
   * Welcome email template for monthly subscribers
   */
  getWelcomeMonthlyTemplate(data) {
    const { accessCode, botPhone } = data;
    
    return {
      subject: 'Your Lockin Habit Tracker Access Code',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #4CAF50; color: white; padding: 20px; text-align: left; border-radius: 5px; }
            .code-box { background: #f4f4f4; border: 2px dashed #4CAF50; padding: 15px; margin: 20px 0; text-align: left; }
            .code { font-size: 24px; font-weight: bold; color: #4CAF50; letter-spacing: 2px; }
            .steps { background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; }
            .step { margin: 10px 0; padding-left: 20px; }
            .footer { text-align: left; color: #666; margin-top: 30px; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to Habit Tracker!</h1>
              <p>Your Monthly Subscription is Active</p>
            </div>
            
            <p>Hi there!</p>
            
            <p>Thank you for subscribing to Habit Tracker! Your monthly subscription ($5/month) is now active.</p>
            
            <div class="code-box">
              <p>Your Access Code:</p>
              <div class="code">${accessCode}</div>
            </div>
            
            <div class="steps">
              <h3>How to Get Started:</h3>
              <div class="step">1. Open WhatsApp on your phone</div>
              <div class="step">2. Send a message to: <strong>${botPhone}</strong></div>
              <div class="step">3. Type exactly: <strong>START ${accessCode}</strong></div>
              <div class="step">4. Follow the setup instructions to add your habits</div>
              <div class="step">5. Start tracking your daily progress!</div>
            </div>
            
            <p><strong>Important:</strong> Keep this access code safe. You'll need it to activate your account.</p>
            
            <p>If you have any questions, feel free to reach out to our support team.</p>
            
            ${getEmailFooterHTML()}
          </div>
        </body>
        </html>
      `,
      text: `
Welcome to Habit Tracker!

Thank you for subscribing! Your monthly subscription ($5/month) is now active.

YOUR ACCESS CODE: ${accessCode}

How to Get Started:
1. Open WhatsApp on your phone
2. Send a message to: ${botPhone}
3. Type exactly: START ${accessCode}
4. Follow the setup instructions to add your habits
5. Start tracking your daily progress!

Important: Keep this access code safe. You'll need it to activate your account.

If you have any questions, feel free to reach out to our support team.

${getEmailFooterText()}
      `
    };
  }

  /**
   * Welcome email template for yearly subscribers (with affiliate info)
   */
  getWelcomeYearlyTemplate(data) {
    const { accessCode, affiliateCode, botPhone } = data;
    
    return {
      subject: 'Your Lockin Habit Tracker Access Code',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #4CAF50; color: white; padding: 20px; text-align: left; border-radius: 5px; }
            .code-box { background: #f4f4f4; border: 2px dashed #4CAF50; padding: 15px; margin: 20px 0; text-align: left; }
            .code { font-size: 24px; font-weight: bold; color: #4CAF50; letter-spacing: 2px; }
            .affiliate-box { background: #fff3cd; border: 2px solid #ffc107; padding: 15px; margin: 20px 0; border-radius: 5px; }
            .steps { background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; }
            .step { margin: 10px 0; padding-left: 20px; }
            .footer { text-align: left; color: #666; margin-top: 30px; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to Habit Tracker!</h1>
              <p>Your Yearly Subscription is Active + You're Now an Affiliate!</p>
            </div>
            
            <p>Hi there!</p>
            
            <p>Thank you for subscribing to Habit Tracker! Your yearly subscription ($30/year) is now active.</p>
            
            <div class="code-box">
              <p>Your Access Code:</p>
              <div class="code">${accessCode}</div>
            </div>
            
            <div class="steps">
              <h3>How to Get Started:</h3>
              <div class="step">1. Open WhatsApp on your phone</div>
              <div class="step">2. Send a message to: <strong>${botPhone}</strong></div>
              <div class="step">3. Type exactly: <strong>START ${accessCode}</strong></div>
              <div class="step">4. Follow the setup instructions to add your habits</div>
              <div class="step">5. Start tracking your daily progress!</div>
            </div>
            
            <div class="affiliate-box">
              <h3>🎉 You're Automatically an Affiliate!</h3>
              <p>As a yearly subscriber, you're enrolled in our affiliate program with a 30% commission!</p>
              <p><strong>Your Affiliate Code:</strong> <span style="font-size: 18px; font-weight: bold;">${affiliateCode}</span></p>
              <p>Share this code with friends and earn 30% commission on every sale!</p>
            </div>
            
            <p><strong>Important:</strong> Keep both codes safe. The access code activates your account, and the affiliate code tracks your referrals.</p>
            
            <p>If you have any questions, feel free to reach out to our support team.</p>
            
            ${getEmailFooterHTML()}
          </div>
        </body>
        </html>
      `,
      text: `
Welcome to Habit Tracker!

Thank you for subscribing! Your yearly subscription ($30/year) is now active.

YOUR ACCESS CODE: ${accessCode}

How to Get Started:
1. Open WhatsApp on your phone
2. Send a message to: ${botPhone}
3. Type exactly: START ${accessCode}
4. Follow the setup instructions to add your habits
5. Start tracking your daily progress!

🎉 YOU'RE AUTOMATICALLY AN AFFILIATE!
As a yearly subscriber, you're enrolled in our affiliate program with a 30% commission!

Your Affiliate Code: ${affiliateCode}
Share this code with friends and earn 30% commission on every sale!

Important: Keep both codes safe. The access code activates your account, and the affiliate code tracks your referrals.

If you have any questions, feel free to reach out to our support team.

${getEmailFooterText()}
      `
    };
  }

  /**
   * Dynamic welcome email template for all subscription types
   */
  getWelcomeDynamicTemplate(data) {
    // SIMPLE VERSION - NO FANCY CSS
    const code = data.primary_access_code || data.accessCode || data.primaryCode || 'HABIT-CODE';
    const subType = data.subscription_type || data.subscriptionTypeDisplay || 'Annual';
    const price = data.subscription_price || data.pricingText || '$39.99/year';
    const phone = data.botPhone || '+***********';

    return {
      subject: 'Your LOCK IN Access Code Is Here',
      html: `<html><body>
<p><strong>LOCK IN</strong><br>
Your Daily Habit Accountability Partner on WhatsApp<br>
Purchase Confirmed</p>

<p><strong>You're in. No more excuses.</strong><br>
Your habit accountability partner is ready to help you build unstoppable momentum.</p>

<p><strong>${subType} Subscription</strong><br>
${price}</p>

<p><strong>YOUR ACCESS CODE:</strong><br>
<strong>${code}</strong></p>

<p><strong>Quick Setup:</strong><br>
1. Open WhatsApp on your phone<br>
2. Text this number: ${phone}<br>
3. Send: START ${code}<br>
4. Follow the setup prompts<br>
5. Start building unstoppable habits!</p>

<p>If you have any questions don't be scared to reply to this email and ask away! Always here to help!</p>

<p><strong>Let's Lock In,<br>Rich</strong></p>

<p><strong>Connect with Rich:</strong><br>
<a href="https://x.com/richvieren">@richvieren</a><br>
<a href="https://substack.com/@vieren">@vieren</a></p>

<p><strong>Earn with Lock In:</strong><br>
Earn a 25% recurring commission on every customer you refer to "Lock In"<br>
<a href="https://aeon.thrivecart.com/lock-in-annual/partner/">Sign up here</a></p>

<p><a href="https://www.lockintracker.com">www.lockintracker.com</a><br>
insta: <a href="https://instagram.com/lockintracker">@lockintracker</a><br>
tiktok: <a href="https://tiktok.com/@lockintracker">@lockintracker</a></p>

<p>© 2025 Lock In. All rights reserved.</p>
</body></html>`,
      text: `
LOCK IN - Purchase Confirmed

${subType} Subscription - ${price}

Your Access Code: ${code}

Quick Setup:
1. Open WhatsApp on your phone
2. Text this number: ${phone}
3. Send: START ${code}
4. Follow the setup prompts
5. Start building unstoppable habits!

If you have any questions don't be scared to reply to this email and ask away! Always here to help!

Let's Lock In,
Rich

Connect with Rich:
x: @richvieren - https://x.com/richvieren
substack: @vieren - https://substack.com/@vieren

Earn with Lock In:
Earn a 25% recurring commission on every customer you refer to "Lock In"
Sign up here: https://aeon.thrivecart.com/lock-in-annual/partner/

www.lockintracker.com
insta: @lockintracker - https://instagram.com/lockintracker
tiktok: @lockintracker - https://tiktok.com/@lockintracker

© 2025 Lock In. All rights reserved.
      `
    };
  }

  /**
   * Payment failed email template
   */
  getPaymentFailedTemplate(data) {
    return {
      subject: 'Payment Failed - Action Required',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body { margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #1a1a1a; background-color: #ffffff; }
            .container { max-width: 600px; margin: 0 auto; padding: 40px 20px; }
          </style>
        </head>
        <body>
          <div class="container">
            <h2 style="margin: 0 0 24px 0; font-size: 28px; font-weight: 600; color: #000000;">Payment Failed - Action Required</h2>
            
            <p style="margin: 0 0 16px 0; font-size: 16px; color: #1a1a1a;">Hi,</p>
            
            <p style="margin: 0 0 16px 0; font-size: 16px; color: #1a1a1a;">We were unable to process your payment for Lock In Habit Tracker.</p>
            
            <p style="margin: 0 0 24px 0; font-size: 16px; color: #1a1a1a;">Please update your payment method to continue using the service and keep building your unstoppable habits.</p>
            
            <p style="margin: 0 0 32px 0; font-size: 16px; color: #1a1a1a;">To update your payment details, please contact us or check your original purchase email for instructions.</p>
            
            ${getEmailFooterHTML()}
          </div>
        </body>
        </html>
      `,
      text: `Payment Failed - Action Required

Hi,

We were unable to process your payment for Lock In Habit Tracker.

Please update your payment method to continue using the service and keep building your unstoppable habits.

To update your payment details, please contact us or check your original purchase email for instructions.

${getEmailFooterText()}`
    };
  }

  /**
   * Subscription cancelled email template
   */
  getSubscriptionCancelledTemplate(data) {
    return {
      subject: 'Your Lock In Subscription Has Been Cancelled',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body { margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #1a1a1a; background-color: #ffffff; }
            .container { max-width: 600px; margin: 0 auto; padding: 40px 20px; }
          </style>
        </head>
        <body>
          <div class="container">
            <h2 style="margin: 0 0 24px 0; font-size: 28px; font-weight: 600; color: #000000;">Your Subscription Has Been Cancelled</h2>
            
            <p style="margin: 0 0 16px 0; font-size: 16px; color: #1a1a1a;">Hi,</p>
            
            <p style="margin: 0 0 16px 0; font-size: 16px; color: #1a1a1a;">Your Lock In subscription has been cancelled as requested.</p>
            
            <p style="margin: 0 0 16px 0; font-size: 16px; color: #1a1a1a;">We're sorry to see you go! Your access will remain active until the end of your current billing period.</p>
            
            <p style="margin: 0 0 24px 0; font-size: 16px; color: #1a1a1a;">Remember, the habits you've built are yours to keep. Keep pushing forward!</p>
            
            <p style="margin: 0 0 32px 0; font-size: 16px; color: #1a1a1a;">If you change your mind, you can always resubscribe at any time to continue your journey.</p>
            
            ${getEmailFooterHTML()}
          </div>
        </body>
        </html>
      `,
      text: `Your Subscription Has Been Cancelled

Hi,

Your Lock In subscription has been cancelled as requested.

We're sorry to see you go! Your access will remain active until the end of your current billing period.

Remember, the habits you've built are yours to keep. Keep pushing forward!

If you change your mind, you can always resubscribe at any time to continue your journey.

${getEmailFooterText()}`
    };
  }

  /**
   * Welcome email template for multiple access codes (with bumps)
   */
  getWelcomeMultiCodeTemplate(data) {
    const { 
      subscription_type,
      subscription_price,
      primary_access_code,
      bonus_codes,
      botPhone
    } = data;
    
    return {
      subject: '🚀 Your LOCK IN Access Code Is Here – Time to Build Unstoppable Habits',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Your LOCK IN Access Codes</title>
          <link href="https://fonts.googleapis.com/css2?family=Archivo+Black:wght@400&family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
        </head>
        <body style="margin: 0; padding: 0; font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #1a1a1a; background-color: #ffffff;">
          <div style="max-width: 600px; margin: 0 auto; background: #ffffff;">
            
            <!-- Header -->
            <div style="padding: 48px 40px; text-align: center; background: #ffffff; border-bottom: 1px solid #f0f0f0;">
              <h1 style="margin: 0; font-family: 'Archivo Black', sans-serif; font-size: 36px; color: #000000; letter-spacing: -1px; line-height: 1.2;">✅ LOCK IN</h1>
              <p style="margin: 8px 0; font-size: 18px; color: #666666; font-weight: 500;">Your Daily Habit Accountability Partner on WhatsApp</p>
              <p style="margin: 16px 0 0 0; font-size: 16px; color: #999999;">Purchase Confirmed</p>
            </div>
            
            <!-- Content -->
            <div style="padding: 40px;">
              
              <!-- Intro Message -->
              <div style="margin-bottom: 32px; text-align: left;">
                <p style="margin: 0; font-size: 18px; color: #1a1a1a; font-weight: 500;">You're in. No more excuses.</p>
                <p style="margin: 8px 0 0 0; font-size: 16px; color: #666666;">Your habit accountability partner is ready to help you build unstoppable momentum.</p>
              </div>
              
              <!-- Subscription Details -->
              <div style="margin-bottom: 32px; padding: 24px; background: #fafafa; border-radius: 8px; text-align: left;">
                <h2 style="margin: 0 0 8px 0; font-size: 20px; font-weight: 600; color: #000000;">${subscription_type} Subscription</h2>
                <p style="margin: 0; font-size: 16px; color: #666666;">${subscription_price}</p>
              </div>
              
              <!-- Primary Access Code -->
              <div style="margin-bottom: 32px; padding: 32px; background: #000000; border-radius: 12px; text-align: left;">
                <p style="margin: 0 0 16px 0; font-size: 14px; color: #ffffff; opacity: 0.8; text-transform: uppercase; letter-spacing: 1px; font-weight: 500;">Your Access Code</p>
                <div style="font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 28px; font-weight: bold; color: #ffffff; letter-spacing: 3px; line-height: 1.2;">${primary_access_code}</div>
              </div>
              
              ${bonus_codes && bonus_codes.length > 0 ? `
              <!-- Bonus Codes -->
              <div style="margin-bottom: 32px;">
                <h3 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 600; color: #000000;">Additional Access Codes</h3>
                ${bonus_codes.map((code, index) => `
                <div style="margin-bottom: 12px; padding: 20px; background: #f8f8f8; border-radius: 8px;">
                  <div style="margin-bottom: 8px;">
                    <span style="font-size: 14px; color: #666666; font-weight: 500;">Additional Code ${index + 1}</span>
                  </div>
                  <div style="font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 18px; font-weight: bold; color: #000000; letter-spacing: 2px; padding: 4px 0;">${code}</div>
                </div>
                `).join('')}
                <p style="margin: 16px 0 0 0; font-size: 14px; color: #666666; text-align: left;">Share these codes with friends or family</p>
              </div>
              ` : ''}
              
              <!-- Setup Instructions -->
              <div style="margin-bottom: 32px; padding: 32px; background: #fafafa; border-radius: 12px;">
                <h3 style="margin: 0 0 24px 0; font-size: 18px; font-weight: 600; color: #000000;">Quick Setup</h3>
                
                <div style="margin-bottom: 16px; display: flex; align-items: flex-start;">
                  <div style="width: 24px; height: 24px; background: #000000; color: #ffffff; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 12px; margin-right: 16px; flex-shrink: 0; margin-top: 2px;">1</div>
                  <p style="margin: 0; font-size: 16px; color: #1a1a1a;">Open <strong>WhatsApp</strong> on your phone</p>
                </div>
                
                <div style="margin-bottom: 16px; display: flex; align-items: flex-start;">
                  <div style="width: 24px; height: 24px; background: #000000; color: #ffffff; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 12px; margin-right: 16px; flex-shrink: 0; margin-top: 2px;">2</div>
                  <p style="margin: 0; font-size: 16px; color: #1a1a1a;">Text this number: <strong>${botPhone}</strong></p>
                </div>
                
                <div style="margin-bottom: 16px; display: flex; align-items: flex-start;">
                  <div style="width: 24px; height: 24px; background: #000000; color: #ffffff; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 12px; margin-right: 16px; flex-shrink: 0; margin-top: 2px;">3</div>
                  <p style="margin: 0; font-size: 16px; color: #1a1a1a;">Send: <code style="background: #e8e8e8; padding: 4px 8px; border-radius: 4px; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 14px;">START ${primary_access_code}</code></p>
                </div>
                
                <div style="margin-bottom: 16px; display: flex; align-items: flex-start;">
                  <div style="width: 24px; height: 24px; background: #000000; color: #ffffff; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 12px; margin-right: 16px; flex-shrink: 0; margin-top: 2px;">4</div>
                  <p style="margin: 0; font-size: 16px; color: #1a1a1a;">Follow the setup prompts</p>
                </div>
                
                <div style="display: flex; align-items: flex-start;">
                  <div style="width: 24px; height: 24px; background: #000000; color: #ffffff; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 12px; margin-right: 16px; flex-shrink: 0; margin-top: 2px;">5</div>
                  <p style="margin: 0; font-size: 16px; color: #1a1a1a;">Start building unstoppable habits!</p>
                </div>
              </div>
            </div>
            
            <!-- Footer -->
            <div style="padding: 0 40px 20px;">
              ${getEmailFooterHTML()}
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
LOCK IN - Purchase Confirmed

${subscription_type} Subscription - ${subscription_price}

Your Access Code: ${primary_access_code}

${bonus_codes && bonus_codes.length > 0 ? `Additional Access Codes:
${bonus_codes.map((code, index) => `Code ${index + 1}: ${code}`).join('\n')}
Share these codes with friends or family

` : ''}Quick Setup:
1. Open WhatsApp on your phone
2. Text this number: ${botPhone}
3. Send: START ${primary_access_code}
4. Follow the setup prompts
5. Start building unstoppable habits!

${getEmailFooterText()}
      `
    };
  }
}

module.exports = new EmailTemplates();