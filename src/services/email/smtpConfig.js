const nodemailer = require('nodemailer');
const logger = require('../../config/logger');

class SMTPConfig {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  /**
   * Initialize email transporter
   */
  initializeTransporter() {
    if (process.env.SMTP_HOST && process.env.SMTP_USER) {
      this.transporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_PORT === '465',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      });

      // Verify transporter configuration
      this.transporter.verify((error) => {
        if (error) {
          logger.error('Email transporter verification failed', { error: error.message });
          this.transporter = null;
        } else {
          logger.info('Email service ready');
        }
      });
    } else {
      logger.warn('Email service not configured - emails will be logged only');
    }
  }

  /**
   * Get the configured transporter
   */
  getTransporter() {
    return this.transporter;
  }
}

module.exports = new SMTPConfig();