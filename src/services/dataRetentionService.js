const pool = require('../db/connection');
const logger = require('../config/logger');

class DataRetentionService {
  
  constructor() {
    this.isRunning = false;
  }

  /**
   * Start the data retention cleanup service
   * Runs daily to clean up expired data
   */
  start() {
    if (this.isRunning) {
      logger.warn('Data retention service already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting data retention service');

    // Run immediately on startup
    this.runCleanup();

    // Schedule to run daily at 2 AM
    const dailyInterval = 24 * 60 * 60 * 1000; // 24 hours
    this.intervalId = setInterval(() => {
      this.runCleanup();
    }, dailyInterval);
  }

  /**
   * Stop the data retention service
   */
  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    logger.info('Data retention service stopped');
  }

  /**
   * Run the cleanup process
   */
  async runCleanup() {
    logger.info('Starting data retention cleanup');

    try {
      const stats = await this.performCleanup();
      logger.info('Data retention cleanup completed', stats);
    } catch (error) {
      logger.error('Data retention cleanup failed', { error: error.message });
    }
  }

  /**
   * Perform the actual cleanup operations
   * @returns {Object} Cleanup statistics
   */
  async performCleanup() {
    const client = await pool.connect();
    const stats = {
      deletedUsers: 0,
      deletedExports: 0,
      deletedRequests: 0,
      deletedAuditLogs: 0,
      cleanupDate: new Date().toISOString()
    };

    try {
      await client.query('BEGIN');

      // 1. Process deletion requests (after 30-day grace period)
      const deletionResult = await client.query(`
        SELECT id FROM users 
        WHERE account_deletion_requested = TRUE 
        AND deletion_request_date < NOW() - INTERVAL '30 days'
      `);

      for (const user of deletionResult.rows) {
        await this.deleteUserCompletely(client, user.id);
        stats.deletedUsers++;
      }

      // 2. Delete inactive users without consent (1 year inactive)
      const inactiveResult = await client.query(`
        DELETE FROM users 
        WHERE last_active < NOW() - INTERVAL '1 year' 
        AND (consent_given = FALSE OR consent_given IS NULL)
        AND account_deletion_requested = FALSE
      `);
      stats.deletedUsers += inactiveResult.rowCount;

      // 3. Clean up expired data exports
      const expiredExports = await client.query(`
        DELETE FROM data_exports 
        WHERE expiry_date < NOW()
        RETURNING id
      `);
      stats.deletedExports = expiredExports.rowCount;

      // 4. Clean up expired data requests
      const expiredRequests = await client.query(`
        DELETE FROM data_requests 
        WHERE expiry_date < NOW() 
        AND status IN ('pending', 'completed')
      `);
      stats.deletedRequests = expiredRequests.rowCount;

      // 5. Clean up old audit logs (keep for 2 years)
      const oldAuditLogs = await client.query(`
        DELETE FROM audit_log 
        WHERE timestamp < NOW() - INTERVAL '2 years'
      `);
      stats.deletedAuditLogs = oldAuditLogs.rowCount;

      // 6. Update retention dates for active users
      await client.query(`
        UPDATE users 
        SET data_retention_date = NOW() + INTERVAL '1 year'
        WHERE last_active > NOW() - INTERVAL '1 month'
        AND consent_given = TRUE
      `);

      // Log the cleanup operation
      await client.query(`
        INSERT INTO audit_log (action, resource_type, notes, timestamp)
        VALUES ('data_retention_cleanup', 'system', $1, NOW())
      `, [JSON.stringify(stats)]);

      await client.query('COMMIT');
      return stats;

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Completely delete a user and all associated data
   * @param {Object} client - Database client
   * @param {number} userId - User ID to delete
   */
  async deleteUserCompletely(client, userId) {
    logger.info('Deleting user completely', { userId });

    // Delete in correct order due to foreign key constraints
    await client.query('DELETE FROM habit_logs WHERE user_id = $1', [userId]);
    await client.query('DELETE FROM habits WHERE user_id = $1', [userId]);
    await client.query('DELETE FROM user_consents WHERE user_id = $1', [userId]);
    await client.query('DELETE FROM data_requests WHERE user_id = $1', [userId]);
    await client.query('DELETE FROM data_exports WHERE user_id = $1', [userId]);
    
    // Update audit logs to remove PII but keep record of deletion
    await client.query(`
      UPDATE audit_log 
      SET notes = 'User data deleted per retention policy'
      WHERE user_id = $1
    `, [userId]);

    // Finally delete the user
    await client.query('DELETE FROM users WHERE id = $1', [userId]);

    logger.info('User deleted successfully', { userId });
  }

  /**
   * Get retention statistics for compliance reporting
   * @returns {Object} Retention statistics
   */
  async getRetentionStats() {
    const client = await pool.connect();

    try {
      const stats = await client.query(`
        SELECT 
          COUNT(*) as total_users,
          COUNT(CASE WHEN last_active > NOW() - INTERVAL '30 days' THEN 1 END) as active_30_days,
          COUNT(CASE WHEN last_active > NOW() - INTERVAL '1 year' THEN 1 END) as active_1_year,
          COUNT(CASE WHEN account_deletion_requested = TRUE THEN 1 END) as deletion_requested,
          COUNT(CASE WHEN consent_given = TRUE THEN 1 END) as consented_users,
          COUNT(CASE WHEN data_retention_date < NOW() THEN 1 END) as expired_retention,
          AVG(EXTRACT(DAYS FROM NOW() - last_active)) as avg_days_inactive
        FROM users
        WHERE current_state != 'BLOCKED_MINOR'
      `);

      const exportStats = await client.query(`
        SELECT 
          COUNT(*) as total_exports,
          COUNT(CASE WHEN expiry_date < NOW() THEN 1 END) as expired_exports,
          SUM(file_size) as total_export_size
        FROM data_exports
      `);

      const auditStats = await client.query(`
        SELECT 
          COUNT(*) as total_audit_logs,
          COUNT(CASE WHEN timestamp < NOW() - INTERVAL '2 years' THEN 1 END) as old_audit_logs,
          MIN(timestamp) as oldest_log,
          MAX(timestamp) as newest_log
        FROM audit_log
      `);

      return {
        users: stats.rows[0],
        exports: exportStats.rows[0],
        audit: auditStats.rows[0],
        generated_at: new Date().toISOString()
      };

    } finally {
      client.release();
    }
  }

  /**
   * Manually trigger cleanup (for testing/admin)
   * @returns {Object} Cleanup results
   */
  async manualCleanup() {
    logger.info('Manual data retention cleanup triggered');
    return await this.performCleanup();
  }
}

module.exports = new DataRetentionService();