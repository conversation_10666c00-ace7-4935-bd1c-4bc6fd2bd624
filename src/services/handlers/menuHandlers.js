const User = require('../../models/User');
const Habit = require('../../models/Habit');
const { STATES, USER_STATUS } = require('../../config/constants');
const logger = require('../../config/logger');

class MenuHandlers {
  // ⚠️  MAIN MENU FORMAT IS FINAL - DO NOT MODIFY ⚠️
  // This function contains LOCKED main menu format. 
  // Backup: lockin-backup-2025-08-15-1535-working-main-menu
  async handleMainMenu(user, message) {
    const input = message.toLowerCase().trim();

    // Check if user is locked
    if (user.status === USER_STATUS.LOCKED) {

      // Start onboarding for new users
      await User.updateStatus(user.id, USER_STATUS.ONBOARDING);
      await User.updateState(user.id, STATES.AWAITING_NAME);
      
      return {
        message: `Welcome to Habit Tracker! 🎯

Let's set up your profile.
What should I call you? (Enter your first name)`,
        newState: STATES.AWAITING_NAME
      };
    }

    // User is unlocked - check if in onboarding flow
    if (user.status === USER_STATUS.ONBOARDING && user.current_state === STATES.SETTING_HABIT) {
      const onboardingHandlers = require('./onboardingHandlers');
      return await onboardingHandlers.handleSettingHabit(user, message);
    } else if (user.status === USER_STATUS.ONBOARDING) {
      // Start proper onboarding flow with welcome message
      await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: 1 });
      return {
        message: `🎯 Welcome to Habit Tracker!

Let's set up your habits to track. You can add up to 5 habits.

📝 Habit 1 of 5

What's the first habit you want to track?

Examples:
• Walk 10000 steps
• Exercise for 30 minutes
• Read for 20 minutes
• Meditate
• No social media before noon

Enter habit 1:`,
        newState: STATES.SETTING_HABIT
      };
    }

    // Check if user has any habits - if not, force them into onboarding
    const habits = await Habit.findByUserId(user.id);
    if (habits.length === 0) {
      // User has no habits - force them into habit setup
      await User.updateStatus(user.id, USER_STATUS.ONBOARDING);
      await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: 1 });
      return {
        message: `🎯 Let's set up your habits!

You need to add at least one habit to start tracking.

📝 Habit 1 of 5

What's the first habit you want to track?

Examples:
• Walk 10000 steps
• Exercise for 30 minutes
• Read for 20 minutes
• Meditate
• No social media before noon

Enter habit 1:`,
        newState: STATES.SETTING_HABIT
      };
    }

    // Active user menu
    if (input === '1' || input === 'log') {
      await User.updateState(user.id, STATES.LOGGING_HABITS);
      const habitHandlers = require('./habitHandlers');
      return await habitHandlers.showTodayHabits(user);
    } else if (input === '2' || input === 'stats' || input === 'progress') {
      await User.updateState(user.id, STATES.STATS_MENU);
      const statsHandlers = require('./statsHandlers');
      return await statsHandlers.handleStatsMenu(user, '');
    } else if (input === '3' || input === 'settings') {
      await User.updateState(user.id, STATES.SETTINGS_MENU);
      return await this.showSettingsMenu(user);
    }

    // Get today's logs to determine completion status
    const todayLogs = await Habit.getTodayLogs(user.id, user.timezone);
    
    // Determine logging status
    let loggedCount = 0;
    let totalCount = 0;
    todayLogs.forEach(log => {
      totalCount++;
      if (log.completed !== null) {
        loggedCount++;
      }
    });
    
    // Determine option 1 text based on logging status
    let option1Text = 'Log today\'s habits';
    if (loggedCount === 0) {
      option1Text = 'Log today\'s habits';
    } else if (loggedCount < totalCount) {
      option1Text = 'Complete today\'s habits';
    } else {
      option1Text = 'Edit today\'s habits';
    }
    
    // Format habit list with status indicators
    const habitsList = todayLogs.map(h => {
      let statusIcon = '⚠️'; // Not logged (warning)
      if (h.completed === true) {
        statusIcon = '✅'; // Completed
      } else if (h.completed === false) {
        statusIcon = '❌'; // Not completed
      }
      return `${statusIcon}${h.habit_number}. ${h.habit_name}`;
    }).join('\n');

    const greeting = user.display_name ? `Hi ${user.display_name}!` : 'Welcome back!';

    // ############################################################################
    // ⚠️  CRITICAL WARNING: MAIN MENU FORMAT IS FINAL - DO NOT MODIFY ⚠️
    // ############################################################################
    // 
    // 🔒 LOCKED: August 15, 2025 - This main menu format is FINAL and FROZEN
    // 🔒 BACKUP: lockin-backup-2025-08-15-1535-working-main-menu
    // 
    // ❌ ABSOLUTELY PROHIBITED CHANGES:
    // - Modifying status icons (⚠️, ✅, ❌)
    // - Changing habit list format "${statusIcon}${habit_number}. ${habit_name}"
    // - Altering greeting structure "${greeting} 👋"
    // - Modifying header "📋 Your Habits (${todayLogs.length}/5):"
    // - Changing footer "Make a selection (reply with a number)."
    // - Breaking dynamic option1Text logic
    // - Rearranging menu structure or layout
    // - Adding/removing emoji elements
    // - Changing indentation or spacing
    // 
    // ✅ ONLY ALLOWED CHANGES:
    // - Minor text updates to option labels (e.g., "Check stats" → "View stats")
    // - Critical bug fixes that preserve exact visual format
    // 
    // 🚨 CONSEQUENCES OF MODIFICATION:
    // - Will break working WhatsApp interface
    // - Violates locked specification
    // - Requires rollback from backup
    // 
    // ⚠️  IF YOU NEED TO MODIFY THIS MENU: STOP! RESTORE FROM BACKUP INSTEAD!
    // ############################################################################
    
    return {
      message: `${greeting} 👋

📋 Your Habits (${todayLogs.length}/5):
${habitsList}

Choose an option:
1️⃣ ${option1Text}
2️⃣ Check stats
3️⃣ Settings

Make a selection (reply with a number).`,
      newState: STATES.MAIN_MENU
    };
  }

  async handleSettingsMenu(user, message) {
    const input = message.toLowerCase().trim();

    if (input === 'menu' || input === 'back') {
      await User.updateState(user.id, STATES.MAIN_MENU);
      return await this.handleMainMenu(user, '');
    }

    const habitNumber = parseInt(input);
    if (habitNumber >= 1 && habitNumber <= 5) {
      await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber });
      return {
        message: `Enter new name for habit #${habitNumber} (or 'delete' to remove it):`,
        newState: STATES.SETTING_HABIT
      };
    }

    if (input === '6' || input === 'timezone') {
      await User.updateState(user.id, STATES.AWAITING_TIMEZONE);
      return {
        message: `Current timezone: ${user.timezone}

Enter new timezone (e.g., America/New_York, Europe/London):`,
        newState: STATES.AWAITING_TIMEZONE
      };
    }

    const habits = await Habit.findByUserId(user.id);
    return await this.showSettingsMenu(user, habits);
  }

  async showSettingsMenu(user, habits = null) {
    if (!habits) {
      habits = await Habit.findByUserId(user.id);
    }

    return {
      message: `⚙️ Settings

📝 Edit Habits:
${Array.from({ length: 5 }, (_, i) => {
  const habit = habits.find(h => h.habit_number === i + 1);
  return `${i + 1}️⃣ ${habit ? habit.habit_name : '[empty]'}`;
}).join('\n')}

6️⃣ Change timezone (current: ${user.timezone})

Reply with a number to edit, or 'menu' to go back.`,
      newState: STATES.SETTINGS_MENU
    };
  }

  // DEVELOPER TEST COMMAND: Reset user to clean slate for testing
  async handleResetTest(user) {
    try {
      const pool = require('../../db/connection');
      
      // Clear ALL habit logs for this user
      const result = await pool.query(
        'DELETE FROM habit_logs WHERE user_id = $1',
        [user.id]
      );
      
      // Reset user state to MAIN_MENU
      await User.updateState(user.id, STATES.MAIN_MENU, {});
      
      // Return main menu with clean slate
      const response = await this.handleMainMenu(user, '');
      
      // Add reset confirmation to the message
      const resetMessage = `🧪 TEST RESET COMPLETE
Cleared ${result.rowCount} habit logs

${response.message}`;
      
      return {
        message: resetMessage,
        newState: response.newState
      };
      
    } catch (error) {
      logger.error('Reset test error', { error: error.message });
      return {
        message: '❌ Reset failed. Please try again.',
        newState: user.current_state
      };
    }
  }

  /*******************************************************************
   * LOCKED: COMPLETION SCREEN - DO NOT MODIFY
   * Dual-message system: Message 1 = shareable content,
   * Message 2 = navigation. sendFollowUp flag triggers Message 2.
   *******************************************************************/
  // COMPLETION SCREEN: Shows when all habits are fully logged
  async showCompletionScreen(user, todayLogs) {
    try {
      // Format habit list with final status
      const habitsList = todayLogs.map(h => {
        const statusIcon = h.completed === true ? '✅' : '❌';
        return `${statusIcon} ${h.habit_number}. ${h.habit_name}`;
      }).join('\n');

      // Calculate actual streak from database
      const currentStreak = await Habit.getCurrentStreak(user.id, user.timezone);
      
      // Calculate actual weekly progress
      const weekLogs = await Habit.getWeekLogs(user.id, user.timezone);
      const daysCompleted = new Set();
      weekLogs.forEach(log => {
        if (log.completed === true) {
          daysCompleted.add(log.log_date.toISOString().split('T')[0]);
        }
      });
      const weeklyProgress = `${daysCompleted.size}/7`;

      // MESSAGE 1: Shareable completion content
      const completionMessage = `🎉 DAY COMPLETE! 

📋 Your Habits (5/5):
${habitsList}

💪 "Success is the sum of small efforts repeated day in and day out." - Robert Collier

🔥 Current streak: ${currentStreak} days
📈 This week: ${weeklyProgress} days complete`;

      // Set state for completion screen navigation
      await User.updateState(user.id, STATES.COMPLETION_SCREEN || 'COMPLETION_SCREEN');

      return {
        message: completionMessage,
        newState: 'COMPLETION_SCREEN',
        sendFollowUp: true // Flag to send MESSAGE 2 immediately
      };

    } catch (error) {
      logger.error('Completion screen error', { error: error.message });
      // Fallback to regular submenu if error
      const habitHandlers = require('./habitHandlers');
      return await habitHandlers.showTodayHabits(user);
    }
  }

  /*******************************************************************
   * LOCKED: COMPLETION SCREEN NAVIGATION - DO NOT MODIFY
   * Handles user input from completion screen.
   * Options: 1 = Edit habits, 2 = Back to menu
   *******************************************************************/
  // Handle completion screen navigation (MESSAGE 2)
  async handleCompletionScreen(user, message) {
    const input = message.toLowerCase().trim();

    if (input === '1' || input === 'edit') {
      // Go back to logging submenu to edit habits
      await User.updateState(user.id, STATES.LOGGING_HABITS);
      const habitHandlers = require('./habitHandlers');
      return await habitHandlers.showTodayHabits(user);
    } else if (input === '2' || input === 'menu') {
      // Go back to main menu
      await User.updateState(user.id, STATES.MAIN_MENU);
      return await this.handleMainMenu(user, '');
    }

    // Show MESSAGE 2: Instructions + navigation
    return {
      message: `Click and hold the previous message to forward or share.

1️⃣ Edit today's habits
2️⃣ Back to menu

Make a selection (reply with a number).`,
      newState: STATES.COMPLETION_SCREEN
    };
  }
}

module.exports = new MenuHandlers();