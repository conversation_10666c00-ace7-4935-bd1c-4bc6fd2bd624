const User = require('../../models/User');
const Habit = require('../../models/Habit');
const AuditLog = require('../../models/AuditLog');
const { STATES, USER_STATUS, AUDIT_EVENTS, HABITS } = require('../../config/constants');
const moment = require('moment-timezone');

class OnboardingHandlers {
  async handleName(user, message) {
    const name = message.trim().substring(0, 50);

    if (name.length < 1) {
      return {
        message: "Please enter a valid name (at least 1 character):",
        newState: STATES.AWAITING_NAME
      };
    }

    await User.setName(user.id, name);
    await User.updateState(user.id, STATES.AWAITING_TIMEZONE);

    // List common timezones
    return {
      message: `Nice to meet you, ${name}! 👋

Now, what's your timezone? This helps with daily reminders.

Common timezones:
• America/New_York (EST/EDT)
• America/Chicago (CST/CDT)
• America/Denver (MST/MDT)
• America/Los_Angeles (PST/PDT)
• Europe/London (GMT/BST)
• Europe/Paris (CET/CEST)
• Asia/Tokyo (JST)
• Australia/Sydney (AEDT/AEST)

Enter your timezone (or type 'UTC' for default):`,
      newState: STATES.AWAITING_TIMEZONE
    };
  }

  async handleTimezone(user, message) {
    const timezone = message.trim();

    // Validate timezone
    if (!moment.tz.zone(timezone)) {
      return {
        message: `❌ Invalid timezone: "${timezone}"

Please enter a valid timezone from the list above, or type 'UTC' for default:`,
        newState: STATES.AWAITING_TIMEZONE
      };
    }

    await User.setTimezone(user.id, timezone);
    // Start with habit 1 automatically
    await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: 1 });

    return {
      message: `✅ Timezone set to ${timezone}

Great! Now let's set up your habits to track.

📝 Habit 1 of 5

What's the first habit you want to track?

Examples:
• Walk 10000 steps
• Exercise for 30 minutes
• Read for 20 minutes
• Meditate
• No social media before noon

Enter habit 1:`,
      newState: STATES.SETTING_HABIT
    };
  }

  async handleOnboardingMenu(user, message) {
    // This state is no longer used in the sequential flow
    // but kept for backward compatibility
    await User.updateStatus(user.id, USER_STATUS.ACTIVE);
    await User.updateState(user.id, STATES.MAIN_MENU);
    
    // Delegate to main menu handler
    const menuHandlers = require('./menuHandlers');
    return await menuHandlers.handleMainMenu(user, '');
  }

  async handleSettingHabit(user, message, isFromSettings = false) {
    const habitName = message.trim().substring(0, HABITS.MAX_NAME_LENGTH);
    const { habitNumber } = user.session_context;

    // Allow skipping habits by typing 'skip' or leaving empty during onboarding
    if (user.status === USER_STATUS.ONBOARDING && 
        (message.toLowerCase().trim() === 'skip' || message.trim() === '')) {
      // Move to next habit or finish if at habit 5
      if (habitNumber < 5) {
        const nextHabitNumber = habitNumber + 1;
        await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: nextHabitNumber });
        return {
          message: `⏭️ Skipped habit ${habitNumber}

📝 Habit ${nextHabitNumber} of 5

What habit do you want to track?
(Type 'skip' to skip this one)

Enter habit ${nextHabitNumber}:`,
          newState: STATES.SETTING_HABIT
        };
      } else {
        // Finished all 5 habits, check if any were added
        const habits = await Habit.findByUserId(user.id);
        if (habits.length === 0) {
          // Must set at least one habit
          await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: 1 });
          return {
            message: `❌ You need at least one habit to continue!

📝 Habit 1 of 5

Please enter at least one habit to track:

Enter habit 1:`,
            newState: STATES.SETTING_HABIT
          };
        }
        // Complete onboarding
        await User.updateStatus(user.id, USER_STATUS.ACTIVE);
        await User.updateState(user.id, STATES.MAIN_MENU);
        return {
          message: `🎉 Excellent! You're all set up!

Your ${habits.length} habit${habits.length > 1 ? 's are' : ' is'} ready to track:
${habits.map(h => `${h.habit_number}. ${h.habit_name}`).join('\n')}

Type 'menu' anytime to see your options.`,
          newState: STATES.MAIN_MENU
        };
      }
    }

    if (habitName.length < 1) {
      return {
        message: `Please enter a valid habit name (at least 1 character):

${user.status === USER_STATUS.ONBOARDING ? '(Or type "skip" to skip this habit)' : ''}`,
        newState: STATES.SETTING_HABIT
      };
    }

    await Habit.upsert(user.id, habitNumber, habitName);
    await AuditLog.log(user.id, AUDIT_EVENTS.HABIT_CREATED, { habitNumber, habitName });

    // If in onboarding, automatically progress to next habit
    if (user.status === USER_STATUS.ONBOARDING) {
      if (habitNumber < 5) {
        // Move to next habit
        const nextHabitNumber = habitNumber + 1;
        await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: nextHabitNumber });
        return {
          message: `✅ Habit ${habitNumber} saved: "${habitName}"

📝 Habit ${nextHabitNumber} of 5

What's your next habit to track?
(Type 'skip' to skip this one)

Enter habit ${nextHabitNumber}:`,
          newState: STATES.SETTING_HABIT
        };
      } else {
        // Finished all 5 habits
        const habits = await Habit.findByUserId(user.id);
        await User.updateStatus(user.id, USER_STATUS.ACTIVE);
        await User.updateState(user.id, STATES.MAIN_MENU);
        return {
          message: `✅ Habit ${habitNumber} saved: "${habitName}"

🎉 Excellent! All habits set up!

Your ${habits.length} habit${habits.length > 1 ? 's' : ''} to track:
${habits.map(h => `${h.habit_number}. ${h.habit_name}`).join('\n')}

Type 'menu' to see your options or start logging!`,
          newState: STATES.MAIN_MENU
        };
      }
    } else if (isFromSettings) {
      // Not in onboarding (editing from settings)
      await User.updateState(user.id, STATES.SETTINGS_MENU);
      return {
        message: `✅ Habit #${habitNumber} updated: "${habitName}"

Type 'menu' to return to the main menu.`,
        newState: STATES.SETTINGS_MENU
      };
    }
    
    // Default case (shouldn't normally reach here)
    return {
      message: `✅ Habit saved: "${habitName}"

Type 'menu' to see your options.`,
      newState: user.current_state
    };
  }
}

module.exports = new OnboardingHandlers();