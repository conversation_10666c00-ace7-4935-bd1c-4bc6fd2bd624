const User = require('../../models/User');
const Habit = require('../../models/Habit');
const { STATES } = require('../../config/constants');
const moment = require('moment-timezone');

class StatsHandlers {
  // MAIN STATS MENU HANDLER
  async handleStatsMenu(user, message) {
    const input = message.toLowerCase().trim();

    // Check total days first to determine menu structure
    const totalDays = await Habit.getTotalDaysLogged(user.id);
    const has100Days = totalDays >= 100;

    if (input === '1' || input === '30') {
      await User.updateState(user.id, STATES.STATS_30_DAY);
      return await this.handleStats30Day(user, '');
    } else if (input === '2') {
      if (has100Days) {
        // User has 100+ days and selected option 2 = 100 Day Overview
        await User.updateState(user.id, STATES.STATS_100_DAY);
        return await this.handleStats100Day(user, '');
      } else {
        // User has < 100 days and selected option 2 = Back to Menu
        await User.updateState(user.id, STATES.MAIN_MENU);
        const menuHandlers = require('./menuHandlers');
        return await menuHandlers.handleMainMenu(user, '');
      }
    } else if (input === '3' && has100Days) {
      // Option 3 only exists when user has 100+ days = Back to Menu
      await User.updateState(user.id, STATES.MAIN_MENU);
      const menuHandlers = require('./menuHandlers');
      return await menuHandlers.handleMainMenu(user, '');
    } else if (input === '100') {
      // Direct "100" command - only allow if user has 100+ days
      if (has100Days) {
        await User.updateState(user.id, STATES.STATS_100_DAY);
        return await this.handleStats100Day(user, '');
      }
    } else if (input === 'back' || input === 'menu') {
      await User.updateState(user.id, STATES.MAIN_MENU);
      const menuHandlers = require('./menuHandlers');
      return await menuHandlers.handleMainMenu(user, '');
    }

    // Get all stats for main menu (totalDays already fetched above)
    const currentStreak = await Habit.getCurrentStreak(user.id, user.timezone);
    const bestStreak = await Habit.getBestStreak(user.id);
    const perfectDaysStats = await Habit.getPerfectDaysStats(user.id, user.timezone);
    const weeklyPerformance = await Habit.getWeeklyPerformance(user.id, user.timezone);
    const habitBreakdown = await Habit.getHabitBreakdown(user.id);
    
    // Get current day number
    const currentDay = moment().tz(user.timezone || 'UTC').format('D');
    
    // Generate motivational quote from database
    const motivationalQuote = '"Success is the sum of small efforts repeated day in and day out." - Robert Collier';

    // Build habit breakdown text with completion status icons
    const habitBreakdownText = habitBreakdown.map(habit => {
      const completionRate = habit.completion_rate || 0;
      const icon = completionRate >= 70 ? '✅' : '❌';
      return `${icon} ${habit.habit_name}: ${completionRate}%`;
    }).join('\n');

    // Determine if 100-day option should be shown (has100Days already declared above)
    const optionText = has100Days ? 
      `1️⃣ 30-Day Stats
2️⃣ 100 Day Overview
3️⃣ Back to Menu` :
      `1️⃣ 30-Day Stats
2️⃣ Back to Menu`;

    return {
      message: `🎯 MY STATS

📅 Day ${currentDay}
🔥 Current Streak: ${currentStreak} days
🏆 Best Streak: ${bestStreak} days
✅ Perfect Days: ${perfectDaysStats.perfect_days}/${perfectDaysStats.total_days} (${perfectDaysStats.completion_rate}%)

📈 RECENT PERFORMANCE
This Week: ${weeklyPerformance.thisWeek.perfect_days}/7 days (${weeklyPerformance.thisWeek.completion_rate}%)
Last Week: ${weeklyPerformance.lastWeek.perfect_days}/7 days (${weeklyPerformance.lastWeek.completion_rate}%)

🎯 HABIT BREAKDOWN
${habitBreakdownText}

💪 ${motivationalQuote}

⚔️ Powered by Lock In

${optionText}

Make a selection (reply with a number).`,
      newState: STATES.STATS_MENU
    };
  }

  // 30-DAY STATS SUBMENU HANDLER
  async handleStats30Day(user, message) {
    const input = message.toLowerCase().trim();

    if (input === '1' || input === 'back') {
      await User.updateState(user.id, STATES.STATS_MENU);
      return await this.handleStatsMenu(user, '');
    }

    // Get 30-day specific stats
    const perfectDaysStats = await Habit.getPerfectDaysStats(user.id, user.timezone);
    const habitBreakdown = await Habit.getHabitBreakdown(user.id);
    const bestStreak = await Habit.getBestStreak(user.id);
    
    // Find top and worst performing habits
    const sortedHabits = habitBreakdown.sort((a, b) => (b.completion_rate || 0) - (a.completion_rate || 0));
    const topHabit = sortedHabits[0] || { habit_name: 'N/A', completion_rate: 0 };
    const worstHabit = sortedHabits[sortedHabits.length - 1] || { habit_name: 'N/A', completion_rate: 0 };
    
    // Calculate recovery rate (simplified)
    const recoveryRate = Math.min(95, Math.max(0, perfectDaysStats.completion_rate));

    return {
      message: `📅 30-DAY STATS

Completion: ${perfectDaysStats.completion_rate}%
Perfect Days: ${perfectDaysStats.perfect_days}/30
Longest Streak: ${bestStreak >= 30 ? '30+' : bestStreak}
Recovery after misses: ${recoveryRate}%
Top Habit: ${topHabit.habit_name} ${Math.round((topHabit.completion_rate || 0) * 30 / 100)}/30
Needs Work: ${worstHabit.habit_name} ${Math.round((worstHabit.completion_rate || 0) * 30 / 100)}/30

1️⃣ Back to Stats

Make a selection (reply with a number).`,
      newState: STATES.STATS_30_DAY
    };
  }

  // 100-DAY OVERVIEW SUBMENU HANDLER
  async handleStats100Day(user, message) {
    const input = message.toLowerCase().trim();

    if (input === '1' || input === 'back') {
      await User.updateState(user.id, STATES.STATS_MENU);
      return await this.handleStatsMenu(user, '');
    }

    // Get 100-day stats
    const perfectDaysStats = await Habit.getPerfectDaysStats(user.id, user.timezone);
    const habitBreakdown = await Habit.getHabitBreakdown(user.id);
    const currentStreak = await Habit.getCurrentStreak(user.id, user.timezone);
    const bestStreak = await Habit.getBestStreak(user.id);

    // Generate 10x10 grid (simplified for demo - would need actual daily data)
    let gridText = '';
    for (let row = 0; row < 10; row++) {
      let rowText = '';
      for (let col = 0; col < 10; col++) {
        // Simplified: use completion rate to determine grid
        const dayIndex = row * 10 + col + 1;
        const isComplete = Math.random() < (perfectDaysStats.completion_rate / 100);
        rowText += isComplete ? '✅' : '❌';
      }
      gridText += rowText + '\n';
    }

    // Build mastery levels text
    const masteryText = habitBreakdown.map(habit => {
      const completionCount = Math.round((habit.completion_rate || 0));
      let masteryLevel = 'BEGINNER';
      if (completionCount >= 90) masteryLevel = 'EXPERT';
      else if (completionCount >= 70) masteryLevel = 'ADVANCED';
      else if (completionCount >= 50) masteryLevel = 'INTERMEDIATE';
      
      return `🎯 ${habit.habit_name}: ${completionCount}/100 (${masteryLevel})`;
    }).join('\n');

    return {
      message: `🏆 100-DAY MILESTONE

Total Days: 100
Perfect Days: ${perfectDaysStats.perfect_days}/100 (${perfectDaysStats.completion_rate}%)
Longest Streak: ${bestStreak} days
Current Streak: ${currentStreak} days

📊 YOUR 100-DAY JOURNEY
(1 checkmark = perfect day with all tasks)

${gridText}

🎯 MASTERY LEVELS
${masteryText}

💪 "100 days of progress!"

1️⃣ Back to Stats

Make a selection (reply with a number).`,
      newState: STATES.STATS_100_DAY
    };
  }
}

module.exports = new StatsHandlers();