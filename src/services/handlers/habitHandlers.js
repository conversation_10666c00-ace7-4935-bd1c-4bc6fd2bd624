const User = require('../../models/User');
const Habit = require('../../models/Habit');
const AuditLog = require('../../models/AuditLog');
const { STATES, USER_STATUS, AUDIT_EVENTS } = require('../../config/constants');
const logger = require('../../config/logger');
const moment = require('moment-timezone');

class HabitHandlers {
  /*******************************************************************
   * LOCKED: HABIT LOGGING INPUT PARSER - DO NOT MODIFY
   * This parsing logic handles both "1y 2n 3y" and "1,3,5" formats.
   * Critical for user experience - any changes break habit tracking.
   *******************************************************************/
  async handleLoggingHabits(user, message) {
    const input = message.toLowerCase().trim();

    if (input === 'menu' || input === 'back') {
      await User.updateState(user.id, STATES.MAIN_MENU);
      const menuHandlers = require('./menuHandlers');
      return await menuHandlers.handleMainMenu(user, '');
    }

    // Parse habit completions - support both formats:
    // Format 1: "1y 2n 3y" (y=yes/completed, n=no/not completed)
    // Format 2: "1,3,5" or "135" (numbers only = completed)
    
    const habits = await Habit.findByUserId(user.id);
    const today = moment.tz(user.timezone).format('YYYY-MM-DD');
    let logsToCreate = [];
    
    // Check for "1y 2n 3y" format first
    const ynMatches = input.match(/(\d+)([yn])/g);
    if (ynMatches && ynMatches.length > 0) {
      // Process "1y 2n 3y" format
      ynMatches.forEach(match => {
        const [, num, yn] = match.match(/(\d+)([yn])/);
        const habitNumber = parseInt(num);
        if (habitNumber >= 1 && habitNumber <= 5) {
          logsToCreate.push({
            habitNumber,
            completed: yn === 'y'
          });
        }
      });
    } else {
      // Fall back to numbers-only format "1,3,5" or "135"
      const completed = new Set();
      const numbers = input.match(/\d/g);
      if (numbers) {
        numbers.forEach(n => {
          const num = parseInt(n);
          if (num >= 1 && num <= 5) {
            completed.add(num);
          }
        });
      }
      
      // Create logs for all habits (completed=true for numbers in set, false for others)
      for (const habit of habits) {
        logsToCreate.push({
          habitNumber: habit.habit_number,
          completed: completed.has(habit.habit_number)
        });
      }
    }

    if (logsToCreate.length > 0) {
      // Apply the logs
      for (const logEntry of logsToCreate) {
        const habit = habits.find(h => h.habit_number === logEntry.habitNumber);
        if (habit) {
          await Habit.logHabit(user.id, habit.id, today, logEntry.completed);
        }
      }

      await AuditLog.log(user.id, AUDIT_EVENTS.HABIT_LOGGED, { 
        date: today, 
        completed: logsToCreate.filter(log => log.completed).map(log => log.habitNumber) 
      });

      /*******************************************************************
       * LOCKED: COMPLETION SCREEN TRIGGER LOGIC - DO NOT MODIFY
       * Triggers completion screen ONLY when ALL habits are logged.
       * This exact logic ensures users see progress screen at right time.
       *******************************************************************/
      // Check if all habits are fully logged (completion screen)
      const updatedLogs = await Habit.getTodayLogs(user.id, user.timezone);
      const hasUnloggedHabits = updatedLogs.some(h => h.completed === null);
      
      // DEBUG: Log completion check details
      console.log('🔍 COMPLETION CHECK:', {
        userId: user.id,
        totalHabits: updatedLogs.length,
        unloggedCount: updatedLogs.filter(h => h.completed === null).length,
        hasUnloggedHabits,
        habitStatuses: updatedLogs.map(h => ({ 
          number: h.habit_number, 
          name: h.habit_name, 
          completed: h.completed 
        }))
      });
      
      if (!hasUnloggedHabits) {
        // All habits logged - show completion screen
        console.log('🎉 TRIGGERING COMPLETION SCREEN');
        const menuHandlers = require('./menuHandlers');
        return await menuHandlers.showCompletionScreen(user, updatedLogs);
      }
      
      // Stay in logging submenu and show updated status
      return await this.showTodayHabits(user);
    }

    return await this.showTodayHabits(user);
  }

  /*******************************************************************
   * LOCKED: LOGGING SUBMENU DISPLAY - DO NOT MODIFY
   * Shows real-time status with ✅/❌/⚠️ icons.
   * Format is critical for user experience.
   *******************************************************************/
  async showTodayHabits(user) {
    const todayLogs = await Habit.getTodayLogs(user.id, user.timezone);
    
    if (todayLogs.length === 0) {
      // No habits - redirect to habit setup
      await User.updateStatus(user.id, USER_STATUS.ONBOARDING);
      await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: 1 });
      return {
        message: `🎯 You need to set up habits first!

📝 Habit 1 of 5

What's the first habit you want to track?

Examples:
• Walk 10000 steps
• Exercise for 30 minutes
• Read for 20 minutes
• Meditate
• No social media before noon

Enter habit 1:`,
        newState: STATES.SETTING_HABIT
      };
    }

    /*******************************************************************
     * LOCKED: REAL-TIME STATUS ICONS - DO NOT MODIFY
     * ⚠️ = Not logged, ✅ = Completed, ❌ = Not completed
     * These icons provide instant visual feedback to users.
     *******************************************************************/
    const habitList = todayLogs.map(h => {
      let statusIcon = '⚠️'; // Not logged (warning)
      if (h.completed === true) {
        statusIcon = '✅'; // Completed
      } else if (h.completed === false) {
        statusIcon = '❌'; // Not completed
      }
      return `${statusIcon} ${h.habit_number}. ${h.habit_name}`;
    }).join('\n');

    return {
      message: `📋 Today's Habits:
${habitList}

Which habits did you complete today?
Reply in format: "1y 2n 3y" (y=yes, n=no)
Type 'menu' to go back.`,
      newState: STATES.LOGGING_HABITS
    };
  }

  async handleViewingProgress(user, message) {
    await User.updateState(user.id, STATES.MAIN_MENU);
    return await this.showProgress(user);
  }

  async showProgress(user) {
    const progress = await Habit.getProgress(user.id, 7, user.timezone);
    
    if (progress.length === 0) {
      // No habits - redirect to habit setup
      await User.updateStatus(user.id, USER_STATUS.ONBOARDING);
      await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: 1 });
      return {
        message: `🎯 No habits to show progress for!

Let's set up your first habit:

📝 Habit 1 of 5

What's the first habit you want to track?

Examples:
• Walk 10000 steps
• Exercise for 30 minutes
• Read for 20 minutes
• Meditate
• No social media before noon

Enter habit 1:`,
        newState: STATES.SETTING_HABIT
      };
    }

    const progressText = progress.map(h => {
      const bar = this.createProgressBar(h.completion_rate);
      return `${h.habit_number}. ${h.habit_name}
   ${bar} ${h.completion_rate}% (${h.completed_days}/${h.total_days} days)`;
    }).join('\n\n');

    return {
      message: `📊 Your 7-Day Progress:

${progressText}

Keep building those habits! 🚀

Type 'menu' to return.`,
      newState: STATES.MAIN_MENU
    };
  }

  createProgressBar(percentage) {
    const filled = Math.round(percentage / 10);
    const empty = 10 - filled;
    return '▓'.repeat(filled) + '░'.repeat(empty);
  }
}

module.exports = new HabitHandlers();