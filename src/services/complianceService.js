const pool = require('../db/connection');
const logger = require('../config/logger');

class ComplianceService {
  
  /**
   * Check if user needs to complete compliance onboarding
   * @param {Object} user - User object
   * @returns {boolean} True if compliance needed
   */
  needsComplianceOnboarding(user) {
    return !user.age_verified || !user.consent_given || !user.terms_accepted;
  }

  /**
   * Handle age verification flow
   * @param {Object} user - User object
   * @param {string} response - User's age response
   * @returns {Object} Response object
   */
  async handleAgeVerification(user, response) {
    const age = parseInt(response.trim());
    
    if (isNaN(age) || age < 13 || age > 120) {
      return {
        message: `Please enter a valid age (just the number, like 25).\n\nYou must be 18 or older to use Lockin.`,
        newState: 'AGE_VERIFICATION'
      };
    }
    
    if (age < 18) {
      // Block minors - COPPA compliance
      await this.blockMinorUser(user);
      
      return {
        message: `Sorry, you must be 18 or older to use Lockin. Your account cannot be activated.`,
        newState: 'BLOCKED_MINOR'
      };
    }
    
    // Age 18+ verified
    await this.recordAgeVerification(user, age);
    
    return {
      message: `By continuing, you agree to our Terms and Privacy Policy.

Reply AGREE to start setting up your habits, or TERMS to read details first.`,
        newState: 'COMBINED_CONSENT'
      };
  }

  /**
   * Handle combined privacy and terms consent
   * @param {Object} user - User object
   * @param {string} response - User's consent response
   * @returns {Object} Response object
   */
  async handleCombinedConsent(user, response) {
    const consent = response.toLowerCase().trim();
    
    // Handle agreement to both terms and privacy
    if (consent === 'agree' || consent === 'yes' || consent === 'accept' || consent === 'y') {
      await this.recordCombinedConsent(user);
      
      return {
        message: `Welcome to Lockin! Let's set up your first habit.

What's the first habit you'd like to track? (e.g., "Drink 2 liters of water")`,
        newState: 'SETTING_HABIT'
      };
    }
    
    // Handle request for terms details
    if (consent === 'terms' || consent === 'terms help' || consent === 'privacy help') {
      return {
        message: `Terms: https://lockintracker.com/terms
Privacy: https://lockintracker.com/privacy

Key points:
• We track your habits and progress
• $5/month or $30/year after trial
• You can delete your data anytime
• We don't sell your information

Reply AGREE to continue or NO to decline.`,
        newState: 'COMBINED_CONSENT'
      };
    }
    
    // Handle rejection
    if (consent === 'no' || consent === 'n' || consent === 'decline') {
      return {
        message: `We need your consent to provide the service. Without it, we cannot activate your account.

Reply AGREE if you change your mind, or TERMS to learn more.`,
        newState: 'COMBINED_CONSENT'
      };
    }
    
    return {
      message: `Please reply AGREE to accept our Terms and Privacy Policy, or TERMS to read them first.`,
      newState: 'COMBINED_CONSENT'
    };
  }

  /**
   * Handle privacy consent (legacy - redirects to combined)
   * @param {Object} user - User object
   * @param {string} response - User's consent response
   * @returns {Object} Response object
   */
  async handlePrivacyConsent(user, response) {
    const consent = response.toLowerCase().trim();
    
    if (consent === 'yes' || consent === 'y' || consent === 'agree' || consent === 'accept') {
      await this.recordPrivacyConsent(user, true);
      
      return {
        message: `✅ *PRIVACY CONSENT RECORDED*

Thank you! Your consent has been recorded with timestamp for legal compliance.

*Your Rights (GDPR/CCPA):*
• "EXPORT MY DATA" - Download all your data
• "DELETE MY DATA" - Permanently delete account
• "STOP" - Opt out of communications
• Update habits anytime

*Next: Terms of Service*

Our Terms protect both you and us:
• Service description and rules
• Payment terms ($5/month or $30/year)
• Limitation of liability
• Your responsibilities

📋 Full terms: https://lockintracker.com/terms

*Do you accept our Terms of Service?*
Reply "ACCEPT TERMS" to continue.`,
        newState: 'TERMS_ACCEPTANCE'
      };
    }
    
    if (consent === 'no' || consent === 'n' || consent === 'refuse' || consent === 'decline') {
      return {
        message: `❌ *CONSENT REQUIRED*

We understand privacy is important, but we need your consent to provide the habit tracking service.

*What we do:*
✅ Minimal data collection only
✅ No selling of your data
✅ Strong security measures
✅ Your right to delete anytime

*What we don't do:*
❌ No excessive tracking
❌ No sharing for marketing
❌ No permanent storage against your will

Without consent, we cannot provide the service.

🔒 Questions? Reply "PRIVACY HELP"
Ready to proceed? Reply "YES"`,
        newState: 'PRIVACY_CONSENT'
      };
    }
    
    if (consent.includes('privacy help') || consent.includes('help') || consent.includes('questions')) {
      return {
        message: `🔒 *PRIVACY HELP*

*What data do we collect?*
• Phone number (WhatsApp requirement)
• Habits you create (core service)
• Daily completion logs (progress tracking)
• Email for payments (subscription only)

*Why do we collect it?*
• Provide habit tracking service
• Process payments
• Send service messages only

*Your rights:*
• Access: "EXPORT MY DATA"
• Delete: "DELETE MY DATA"  
• Correct: Update habits anytime
• Opt-out: "STOP" messaging

*Security:*
• Encrypted data transmission
• Secure servers
• No unauthorized sharing
• Regular security reviews

*Questions answered?*
Reply "YES" to consent or ask more questions.`,
        newState: 'PRIVACY_CONSENT'
      };
    }
    
    return {
      message: `I didn't understand that response.

Please reply:
• "YES" to consent to our privacy practices
• "NO" if you don't consent
• "PRIVACY HELP" for more information`,
        newState: 'PRIVACY_CONSENT'
    };
  }

  /**
   * Handle terms acceptance
   * @param {Object} user - User object  
   * @param {string} response - User's terms response
   * @returns {Object} Response object
   */
  async handleTermsAcceptance(user, response) {
    const terms = response.toLowerCase().trim();
    
    if (terms.includes('accept') || terms === 'yes' || terms === 'y' || terms === 'agree') {
      await this.recordTermsAcceptance(user);
      
      return {
        message: `🎉 *WELCOME TO LOCKIN!*

✅ Age verified (18+)
✅ Privacy consent given  
✅ Terms accepted

You're all set! Your account is now fully activated and compliant.

*What's next?*

1️⃣ *Set up your daily habits* (up to 5)
2️⃣ *Start tracking daily*
3️⃣ *Build consistency and momentum*

*Quick Start:*
• Reply "MENU" to see all options
• Reply "HABITS" to set up your first habit
• Reply "HELP" anytime for assistance

*Your Data Rights:*
Remember, you can always:
• "EXPORT MY DATA" - Download everything
• "DELETE MY DATA" - Permanently delete account
• "STOP" - Opt out of messages

Ready to build better habits? 💪

Reply "HABITS" to get started!`,
        newState: 'MAIN_MENU'
      };
    }
    
    if (terms === 'no' || terms === 'n' || terms === 'decline' || terms === 'refuse') {
      return {
        message: `❌ *TERMS REQUIRED*

Sorry, you must accept our Terms of Service to use Lockin.

*Our terms are fair and standard:*
• Describe the service clearly
• Protect both you and us legally
• Include reasonable liability limits
• Allow you to cancel anytime

Without terms agreement, we cannot activate your account.

📋 Read full terms: https://lockintracker.com/terms
🔒 Privacy policy: https://lockintracker.com/privacy

Ready to accept? Reply "ACCEPT TERMS"`,
        newState: 'TERMS_ACCEPTANCE'
      };
    }
    
    return {
      message: `I didn't understand that response.

Please reply:
• "ACCEPT TERMS" to accept our Terms of Service
• "NO" if you don't accept (account cannot be activated)

📋 Read terms: https://lockintracker.com/terms`,
      newState: 'TERMS_ACCEPTANCE'
    };
  }

  /**
   * Record age verification in database
   * @param {Object} user - User object
   * @param {number} age - Verified age
   */
  async recordAgeVerification(user, age) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      
      await client.query(
        `UPDATE users SET 
         age_verified = TRUE,
         age_verification_date = NOW(),
         current_state = 'COMBINED_CONSENT'
         WHERE id = $1`,
        [user.id]
      );
      
      await client.query(
        `INSERT INTO user_consents (user_id, consent_type, consent_given, consent_method, notes)
         VALUES ($1, 'age', TRUE, 'whatsapp', $2)`,
        [user.id, `Age verified: ${age} years old`]
      );
      
      await client.query(
        `INSERT INTO audit_log (user_id, action, resource_type, notes)
         VALUES ($1, 'age_verified', 'user', $2)`,
        [user.id, `User verified age: ${age}`]
      );
      
      await client.query('COMMIT');
      logger.info('Age verification recorded', { userId: user.id, age });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Age verification failed', { userId: user.id, error: error.message });
    } finally {
      client.release();
    }
  }

  /**
   * Record privacy consent in database
   * @param {Object} user - User object
   * @param {boolean} consented - Consent status
   */
  async recordPrivacyConsent(user, consented) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      
      await client.query(
        `UPDATE users SET 
         consent_given = $2,
         consent_timestamp = NOW(),
         consent_version = '1.0'
         WHERE id = $1`,
        [user.id, consented]
      );
      
      await client.query(
        `INSERT INTO user_consents (user_id, consent_type, consent_given, consent_method, consent_version, notes)
         VALUES ($1, 'privacy', $2, 'whatsapp', '1.0', 'User consent for privacy policy v1.0')`,
        [user.id, consented]
      );
      
      await client.query(
        `INSERT INTO audit_log (user_id, action, resource_type, notes)
         VALUES ($1, 'privacy_consent', 'user', $2)`,
        [user.id, `Privacy consent: ${consented ? 'granted' : 'denied'}`]
      );
      
      await client.query('COMMIT');
      logger.info('Privacy consent recorded', { userId: user.id, consented });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Privacy consent failed', { userId: user.id, error: error.message });
    } finally {
      client.release();
    }
  }

  /**
   * Record combined privacy and terms consent
   * @param {Object} user - User object
   */
  async recordCombinedConsent(user) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      
      // Update user with both consents and activate account
      await client.query(
        `UPDATE users SET 
         age_verified = TRUE,
         consent_given = TRUE,
         consent_timestamp = NOW(),
         consent_version = '1.0',
         terms_accepted = TRUE,
         terms_version = '1.0',
         terms_accepted_date = NOW(),
         current_state = 'SETTING_HABIT',
         status = 'ACTIVE'
         WHERE id = $1`,
        [user.id]
      );
      
      // Record privacy consent
      await client.query(
        `INSERT INTO user_consents (user_id, consent_type, consent_given, consent_method, consent_version, notes)
         VALUES ($1, 'privacy', TRUE, 'whatsapp', '1.0', 'Combined consent - Privacy Policy v1.0')`,
        [user.id]
      );
      
      // Record terms consent
      await client.query(
        `INSERT INTO user_consents (user_id, consent_type, consent_given, consent_method, consent_version, notes)
         VALUES ($1, 'terms', TRUE, 'whatsapp', '1.0', 'Combined consent - Terms of Service v1.0')`,
        [user.id]
      );
      
      // Audit log
      await client.query(
        `INSERT INTO audit_log (user_id, action, resource_type, notes)
         VALUES ($1, 'combined_consent', 'user', 'User accepted both Terms and Privacy Policy')`,
        [user.id]
      );
      
      await client.query('COMMIT');
      logger.info('Combined consent recorded', { userId: user.id });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Combined consent failed', { userId: user.id, error: error.message });
    } finally {
      client.release();
    }
  }

  /**
   * Record terms acceptance in database
   * @param {Object} user - User object
   */
  async recordTermsAcceptance(user) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      
      await client.query(
        `UPDATE users SET 
         terms_accepted = TRUE,
         terms_version = '1.0',
         terms_accepted_date = NOW(),
         current_state = 'MAIN_MENU'
         WHERE id = $1`,
        [user.id]
      );
      
      await client.query(
        `INSERT INTO user_consents (user_id, consent_type, consent_given, consent_method, consent_version, notes)
         VALUES ($1, 'terms', TRUE, 'whatsapp', '1.0', 'User accepted Terms of Service v1.0')`,
        [user.id]
      );
      
      await client.query(
        `INSERT INTO audit_log (user_id, action, resource_type, notes)
         VALUES ($1, 'terms_accepted', 'user', 'User accepted Terms of Service v1.0')`,
        [user.id]
      );
      
      await client.query('COMMIT');
      logger.info('Terms acceptance recorded', { userId: user.id });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Terms acceptance failed', { userId: user.id, error: error.message });
    } finally {
      client.release();
    }
  }

  /**
   * Block minor user account
   * @param {Object} user - User object
   */
  async blockMinorUser(user) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      
      await client.query(
        `UPDATE users SET 
         status = 'BLOCKED',
         current_state = 'BLOCKED_MINOR'
         WHERE id = $1`,
        [user.id]
      );
      
      await client.query(
        `INSERT INTO audit_log (user_id, action, resource_type, notes)
         VALUES ($1, 'account_blocked', 'user', 'Account blocked - user under 18')`,
        [user.id]
      );
      
      await client.query('COMMIT');
      logger.info('Minor user blocked', { userId: user.id });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Block minor user failed', { userId: user.id, error: error.message });
    } finally {
      client.release();
    }
  }

  /**
   * Get compliance status for user
   * @param {Object} user - User object
   * @returns {Object} Compliance status
   */
  getComplianceStatus(user) {
    return {
      ageVerified: user.age_verified,
      consentGiven: user.consent_given,
      termsAccepted: user.terms_accepted,
      isCompliant: user.age_verified && user.consent_given && user.terms_accepted,
      needsOnboarding: this.needsComplianceOnboarding(user)
    };
  }

  /**
   * Handle compliance state routing
   * @param {Object} user - User object
   * @param {string} message - User message
   * @returns {Object} Response object
   */
  async handleComplianceFlow(user, message) {
    const state = user.current_state;
    
    switch (state) {
      case 'AGE_VERIFICATION':
        return await this.handleAgeVerification(user, message);
      
      case 'PRIVACY_CONSENT':
      case 'COMBINED_CONSENT':
        return await this.handleCombinedConsent(user, message);
        
      case 'TERMS_ACCEPTANCE':
        return await this.handleTermsAcceptance(user, message);
        
      default:
        // Check if user needs compliance onboarding
        if (this.needsComplianceOnboarding(user)) {
          return await this.startComplianceOnboarding(user);
        }
        return null; // Not a compliance flow
    }
  }

  /**
   * Start compliance onboarding for new users
   * @param {Object} user - User object
   * @returns {Object} Response object
   */
  async startComplianceOnboarding(user) {
    // Update user state
    await pool.query(
      'UPDATE users SET current_state = $1 WHERE id = $2',
      ['AGE_VERIFICATION', user.id]
    );
    
    return {
      message: `Welcome! Please confirm you're 18+ to continue. Just type your age.`,
      newState: 'AGE_VERIFICATION'
    };
  }
}

module.exports = new ComplianceService();