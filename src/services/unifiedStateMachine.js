/**
 * UnifiedStateMachine - Single entry point for all state machine functionality
 * Replaces stateMachine.js, stateMachineCompliant.js, stateMachineWithPayments.js, stateMachinePaymentEnforced.js
 */

const StateMachineFactory = require('../core/factories/StateMachineFactory');
const logger = require('../config/logger');

class UnifiedStateMachine {
  constructor() {
    // Create the appropriate strategy based on environment
    this.strategy = StateMachineFactory.createFromEnv();
    
    logger.info('Unified State Machine initialized', {
      strategy: this.strategy.constructor.name,
      compliance: process.env.ENABLE_COMPLIANCE !== 'false',
      payment: process.env.ENABLE_PAYMENT_ENFORCEMENT === 'true'
    });
  }

  /**
   * Process a message from a user
   * @param {Object} user - User object from database
   * @param {string} message - User's message
   * @returns {Promise<Object>} Response object with message and media
   */
  async processMessage(user, message) {
    return await this.strategy.processMessage(user, message);
  }

  /**
   * Get the current strategy name (for debugging/logging)
   * @returns {string} Strategy name
   */
  getStrategyName() {
    return this.strategy.constructor.name;
  }

  /**
   * Reload strategy based on new configuration
   * @param {Object} options - Configuration options
   */
  reloadStrategy(options = {}) {
    this.strategy = StateMachineFactory.create(options);
    logger.info('State machine strategy reloaded', {
      strategy: this.strategy.constructor.name,
      options
    });
  }
}

// Export singleton instance
module.exports = new UnifiedStateMachine();