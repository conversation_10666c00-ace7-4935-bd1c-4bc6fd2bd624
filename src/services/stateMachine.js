const User = require('../models/User');
const AuditLog = require('../models/AuditLog');
const { STATES, AUDIT_EVENTS } = require('../config/constants');
const logger = require('../config/logger');

// Import handler modules
const menuHandlers = require('./handlers/menuHandlers');
const onboardingHandlers = require('./handlers/onboardingHandlers');
const habitHandlers = require('./handlers/habitHandlers');
const statsHandlers = require('./handlers/statsHandlers');

class StateMachine {
  constructor() {
    // Map states to their handler functions
    this.transitions = {
      [STATES.MAIN_MENU]: menuHandlers.handleMainMenu.bind(menuHandlers),
      [STATES.AWAITING_NAME]: onboardingHandlers.handleName.bind(onboardingHandlers),
      [STATES.AWAITING_TIMEZONE]: onboardingHandlers.handleTimezone.bind(onboardingHandlers),
      [STATES.ONBOARDING_MENU]: onboardingHandlers.handleOnboardingMenu.bind(onboardingHandlers),
      [STATES.SETTING_HABIT]: this.handleSettingHabit.bind(this),
      [STATES.LOGGING_HABITS]: habitHandlers.handleLoggingHabits.bind(habitHandlers),
      [STATES.VIEWING_PROGRESS]: habitHandlers.handleViewingProgress.bind(habitHandlers),
      [STATES.STATS_MENU]: statsHandlers.handleStatsMenu.bind(statsHandlers),
      [STATES.STATS_30_DAY]: statsHandlers.handleStats30Day.bind(statsHandlers),
      [STATES.STATS_100_DAY]: statsHandlers.handleStats100Day.bind(statsHandlers),
      [STATES.SETTINGS_MENU]: menuHandlers.handleSettingsMenu.bind(menuHandlers),
      [STATES.COMPLETION_SCREEN]: menuHandlers.handleCompletionScreen.bind(menuHandlers)
    };
  }

  async processMessage(user, message) {
    try {
      // Update last active
      await User.updateLastActive(user.id);

      // DEVELOPER TEST COMMAND: Reset user to clean slate
      if (message.trim().toUpperCase() === 'RESET_TEST') {
        return await menuHandlers.handleResetTest(user);
      }

      // Get the handler for current state
      const handler = this.transitions[user.current_state] || menuHandlers.handleMainMenu.bind(menuHandlers);
      const response = await handler(user, message);

      return response;
    } catch (error) {
      logger.error('State machine error', { 
        userId: user.id, 
        state: user.current_state, 
        error: error.message 
      });
      
      await AuditLog.log(user.id, AUDIT_EVENTS.ERROR, {
        state: user.current_state,
        error: error.message
      });

      return {
        message: "Sorry, something went wrong. Please try again or type 'menu' to return to the main menu.",
        newState: user.current_state
      };
    }
  }

  // Special handler for SETTING_HABIT state - routes to appropriate handler based on context
  async handleSettingHabit(user, message) {
    // Check if this is from settings menu (not onboarding)
    const isFromSettings = user.status !== 'ONBOARDING' && user.current_state === STATES.SETTINGS_MENU;
    
    return await onboardingHandlers.handleSettingHabit(user, message, isFromSettings);
  }
}

module.exports = new StateMachine();