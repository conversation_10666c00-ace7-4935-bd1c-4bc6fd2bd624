require('dotenv').config();

const express = require('express');
const helmet = require('helmet');
const path = require('path');
const logger = require('./config/logger');
const webhookController = require('./controllers/webhookControllerCompliant'); // Updated to compliant version
const fastspringController = require('./controllers/fastspringController');
const thrivecartController = require('./controllers/thrivecartController');
const sessionManager = require('./services/sessionManager');
const paymentService = require('./services/paymentService');
const emailService = require('./services/emailService');
const enhancedRetentionService = require('./services/enhancedRetentionService');
const complianceAuditService = require('./services/complianceAuditService');
const { validateWebhook } = require('./middleware/validation');
const { checkStopKeywords, checkStartKeyword, checkMessageWindow } = require('./middleware/compliance');
const { globalRateLimiter, perUserRateLimiter } = require('./middleware/rateLimiter');
const AuthMiddleware = require('./middleware/auth');

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet());

// Parse URL-encoded bodies (for Twilio webhooks)
app.use(express.urlencoded({ extended: false }));
app.use(express.json());

// Serve static legal documents
app.use('/legal', express.static(path.join(__dirname, '../public/legal')));

// Redirect convenience URLs
app.get('/privacy', (req, res) => {
  res.redirect('/legal/privacy-policy.html');
});

app.get('/terms', (req, res) => {
  res.redirect('/legal/terms-of-service.html');
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// WhatsApp webhook endpoint with all middleware
app.post('/webhook/whatsapp',
  globalRateLimiter,                        // Global rate limiting
  perUserRateLimiter,                       // Per-user rate limiting
  validateWebhook,                          // Input validation
  webhookController.verifyWebhook,          // Twilio signature verification
  checkStopKeywords,                        // STOP keyword compliance
  checkStartKeyword,                        // START keyword handling
  checkMessageWindow,                       // 24-hour window check
  webhookController.handleIncomingMessage.bind(webhookController)
);

// FastSpring webhook endpoint
app.post('/webhook/fastspring',
  express.json({ limit: '10mb' }),
  fastspringController.handleWebhook.bind(fastspringController)
);

// ThriveCart webhook endpoint - supports both HEAD and POST
app.head('/webhook/thrivecart', (req, res) => {
  res.status(200).send();
});

app.post('/webhook/thrivecart',
  express.urlencoded({ extended: true, limit: '10mb' }),
  thrivecartController.handleWebhook.bind(thrivecartController)
);

// Admin login endpoint (for getting JWT tokens)
app.post('/admin/login', AuthMiddleware.handleAdminLogin);

// Compliance monitoring endpoints (admin only - now with authentication)
if (process.env.NODE_ENV === 'development' || process.env.ADMIN_ACCESS === 'true') {
  // Apply authentication middleware to all admin routes
  app.use('/admin/*', AuthMiddleware.authenticateAdmin);
  
  // Compliance dashboard
  app.get('/admin/compliance/stats', async (req, res) => {
    try {
      const auditStats = await complianceAuditService.getAuditStats();
      const retentionStats = await enhancedRetentionService.getRetentionStats();
      const minimizationRecs = await enhancedRetentionService.getDataMinimizationRecommendations();
      
      res.json({
        audit: auditStats,
        retention: retentionStats,
        recommendations: minimizationRecs,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Compliance report
  app.get('/admin/compliance/report', async (req, res) => {
    try {
      const { start, end } = req.query;
      const startDate = start || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
      const endDate = end || new Date().toISOString();
      
      const report = await complianceAuditService.generateComplianceReport(startDate, endDate);
      res.json(report);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Manual retention cleanup
  app.post('/admin/compliance/cleanup', async (req, res) => {
    try {
      const { type = 'regular' } = req.body;
      const result = await enhancedRetentionService.manualCleanup(type);
      res.json({ success: true, result });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Check retention compliance
  app.get('/admin/compliance/check', async (req, res) => {
    try {
      const compliance = await enhancedRetentionService.checkRetentionCompliance();
      res.json(compliance);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
}

// Test endpoints (only available in test mode)
if (process.env.PAYMENT_TEST_MODE === 'true') {
  // Test webhook simulator for FastSpring
  app.post('/test/webhook',
    express.json(),
    fastspringController.testWebhook.bind(fastspringController)
  );

  // Test webhook simulator for ThriveCart
  app.post('/test/webhook/thrivecart',
    express.json(),
    thrivecartController.testWebhook.bind(thrivecartController)
  );

  // Test status endpoint
  app.get('/test/status', async (req, res) => {
    const pool = require('./db/connection');
    try {
      const stats = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM paid_users) as paid_users,
          (SELECT COUNT(*) FROM access_codes) as access_codes,
          (SELECT COUNT(*) FROM access_codes WHERE used_by_phone IS NOT NULL) as used_codes,
          (SELECT COUNT(*) FROM email_queue WHERE status = 'pending') as pending_emails
      `);
      
      res.json({
        testMode: true,
        stats: stats.rows[0],
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Create test payment
  app.post('/test/create-payment', express.json(), async (req, res) => {
    const { email, subscriptionType = 'monthly' } = req.body;
    
    if (!email) {
      return res.status(400).json({ error: 'Email required' });
    }

    try {
      // Simulate FastSpring webhook
      const testPayload = {
        events: [{
          type: 'order.completed',
          data: {
            id: `TEST-${Date.now()}`,
            customer: {
              id: `CUST-${Date.now()}`,
              email
            },
            items: [{
              product: subscriptionType === 'yearly' ? 'habit-tracker-yearly' : 'habit-tracker-monthly',
              total: subscriptionType === 'yearly' ? 30.00 : 5.00,
              subscription: `SUB-${Date.now()}`
            }],
            currency: 'USD',
            total: subscriptionType === 'yearly' ? 30.00 : 5.00
          }
        }]
      };

      // Process webhook
      req.body = testPayload;
      await fastspringController.handleWebhook(req, res);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Clear test data
  app.delete('/test/clear-data', async (req, res) => {
    const pool = require('./db/connection');
    try {
      await pool.query("DELETE FROM affiliate_referrals WHERE test_mode = true");
      await pool.query("DELETE FROM email_queue WHERE template_data::text LIKE '%TEST-%'");
      await pool.query("DELETE FROM webhook_events WHERE test_mode = true");
      await pool.query("DELETE FROM payment_transactions WHERE fastspring_order_id LIKE 'TEST-%'");
      await pool.query("DELETE FROM access_codes WHERE code IN (SELECT access_code FROM paid_users WHERE test_mode = true)");
      await pool.query("DELETE FROM paid_users WHERE test_mode = true");
      
      res.json({ message: 'Test data cleared' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
}

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error', { 
    error: err.message, 
    stack: err.stack,
    url: req.url,
    method: req.method
  });
  
  res.status(500).json({ 
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Not found' });
});

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  logger.info(`Received ${signal}, starting graceful shutdown...`);
  
  // Stop services
  sessionManager.stop();
  enhancedRetentionService.stop();
  
  // Close database connections
  const pool = require('./db/connection');
  await pool.end();
  
  // Stop server
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
  
  // Force shutdown after 10 seconds
  setTimeout(() => {
    logger.error('Forced shutdown after timeout');
    process.exit(1);
  }, 10000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', { error: error.message, stack: error.stack });
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection', { reason, promise });
});

// Start server
const server = app.listen(PORT, () => {
  logger.info(`WhatsApp Habit Tracker server running on port ${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
  
  // Start session manager
  sessionManager.start();
  
  // Start payment services
  paymentService.startSubscriptionChecker();
  emailService.startQueueProcessor();
  
  // Start enhanced data retention service
  enhancedRetentionService.start();
  
  // Log important configuration
  logger.info('Configuration loaded', {
    database: process.env.DATABASE_URL ? 'Connected' : 'Not configured',
    twilio: process.env.TWILIO_AUTH_TOKEN ? 'Configured' : 'Not configured',
    fastspring: process.env.FASTSPRING_WEBHOOK_SECRET ? 'Configured' : 'Not configured',
    paymentTestMode: process.env.PAYMENT_TEST_MODE === 'true',
    sessionTimeout: '30 minutes',
    rateLimit: '100 requests per 15 minutes'
  });
});

module.exports = app;