const User = require('../models/User');
const AuditLog = require('../models/AuditLog');
const { STOP_KEYWORDS, AUDIT_EVENTS } = require('../config/constants');
const logger = require('../config/logger');

// Check for STOP keywords and handle opt-out
async function checkStopKeywords(req, res, next) {
  const message = req.body.Body;
  const phone = req.body.From;
  
  // Check if message contains any STOP keywords
  const messageUpper = message.toUpperCase().trim();
  const isStopRequest = STOP_KEYWORDS.some(keyword => 
    messageUpper === keyword || messageUpper.startsWith(keyword + ' ')
  );
  
  if (isStopRequest) {
    try {
      // Find user and opt them out
      const user = await User.findByPhone(phone);
      
      if (user && user.status !== 'PAUSED') {
        await User.optOut(user.id);
        await AuditLog.log(user.id, AUDIT_EVENTS.USER_OPTED_OUT, {
          keyword: messageUpper.substring(0, 20)
        });
        
        logger.info('User opted out', { userId: user.id });
      }
      
      // Send opt-out confirmation (required by WhatsApp Business API)
      const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Message>You have been unsubscribed. You will no longer receive messages from this service. Reply START to resubscribe.</Message>
</Response>`;
      
      res.type('text/xml');
      res.send(twiml);
      return; // Stop processing
    } catch (error) {
      logger.error('Error processing opt-out', { error: error.message });
      // Continue processing even if opt-out fails
    }
  }
  
  next();
}

// Check for START keyword to reactivate or activate access code
async function checkStartKeyword(req, res, next) {
  const message = req.body.Body;
  const phone = req.body.From;
  
  const messageUpper = message.toUpperCase().trim();
  
  // Regular START command for reactivation
  if (messageUpper === 'START' || messageUpper === 'SUBSCRIBE') {
    try {
      const user = await User.findByPhone(phone);
      
      if (user && user.status === 'PAUSED') {
        // Reactivate user
        await User.updateStatus(user.id, 'ACTIVE');
        
        logger.info('User reactivated', { userId: user.id });
        
        const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Message>Welcome back! You have been resubscribed to the Habit Tracker. Type 'menu' to see your options.</Message>
</Response>`;
        
        res.type('text/xml');
        res.send(twiml);
        return;
      }
    } catch (error) {
      logger.error('Error processing reactivation', { error: error.message });
    }
  }
  
  next();
}

// Check 24-hour message window (WhatsApp Business API compliance)
async function checkMessageWindow(req, res, next) {
  // In production, you would check if the user has sent a message in the last 24 hours
  // For this implementation, we'll allow all messages as we're responding to user-initiated messages
  
  // The 24-hour window applies to business-initiated messages
  // User-initiated conversations allow responses within 24 hours of the last user message
  
  next();
}

module.exports = {
  checkStopKeywords,
  checkStartKeyword,
  checkMessageWindow
};