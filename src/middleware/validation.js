const Joi = require('joi');
const logger = require('../config/logger');

// E.164 phone format validation (supports WhatsApp prefix)
const phoneSchema = Joi.string()
  .pattern(/^(whatsapp:)?\+[1-9]\d{1,14}$/)
  .required();

// WhatsApp webhook validation schema
const webhookSchema = Joi.object({
  Body: Joi.string().max(4096).required(),
  From: phoneSchema,
  To: phoneSchema,
  MessageSid: Joi.string().required(),
  AccountSid: Joi.string().required(),
  NumMedia: Joi.string().optional(),
  SmsMessageSid: Joi.string().optional(),
  SmsStatus: Joi.string().optional(),
  MessageStatus: Joi.string().optional(),
  ProfileName: Joi.string().optional(),
  WaId: Joi.string().optional()
}).unknown(true); // Allow additional fields

// Input sanitization
function sanitizeInput(text) {
  if (!text) return '';
  
  // Remove any potential SQL injection attempts
  let sanitized = text.trim();
  
  // Remove common SQL keywords if they appear alone
  const sqlKeywords = ['DROP', 'DELETE', 'INSERT', 'UPDATE', 'SELECT', 'ALTER'];
  const upperText = sanitized.toUpperCase();
  
  for (const keyword of sqlKeywords) {
    if (upperText === keyword || upperText.startsWith(keyword + ' ')) {
      logger.warn('Potential SQL injection attempt blocked', { 
        attempt: sanitized.substring(0, 50) 
      });
      return '';
    }
  }
  
  // Remove any HTML/script tags
  sanitized = sanitized.replace(/<[^>]*>/g, '');
  
  // Limit length
  sanitized = sanitized.substring(0, 1000);
  
  return sanitized;
}

// Middleware to validate webhook requests
const validateWebhook = (req, res, next) => {
  const { error } = webhookSchema.validate(req.body);
  
  if (error) {
    logger.warn('Invalid webhook request', { 
      error: error.details[0].message,
      ip: req.ip 
    });
    return res.status(400).send('Invalid request');
  }
  
  // Sanitize the message body
  req.body.Body = sanitizeInput(req.body.Body);
  
  next();
};

// Validate phone number format
const validatePhone = (phone) => {
  const { error } = phoneSchema.validate(phone);
  return !error;
};

module.exports = {
  validateWebhook,
  validatePhone,
  sanitizeInput
};