/**
 * Authentication Middleware - JWT-based authentication for admin endpoints
 */

const jwt = require('jsonwebtoken');
const logger = require('../config/logger');
const AuditLog = require('../models/AuditLog');
const { AUDIT_EVENTS } = require('../config/constants');

class AuthMiddleware {
  /**
   * Generate a JWT token
   * @param {Object} payload - Token payload
   * @param {string} payload.userId - User ID
   * @param {string} payload.role - User role (admin, user)
   * @param {Object} options - Token options
   * @returns {string} JWT token
   */
  static generateToken(payload, options = {}) {
    const secret = process.env.JWT_SECRET || 'default-secret-change-in-production';
    const defaultOptions = {
      expiresIn: '24h',
      issuer: 'lock-in-habit-tracker',
      ...options
    };

    return jwt.sign(payload, secret, defaultOptions);
  }

  /**
   * Verify and decode a JWT token
   * @param {string} token - JWT token
   * @returns {Object} Decoded token payload
   * @throws {Error} If token is invalid
   */
  static verifyToken(token) {
    const secret = process.env.JWT_SECRET || 'default-secret-change-in-production';
    
    try {
      return jwt.verify(token, secret);
    } catch (error) {
      logger.error('JWT verification failed', { error: error.message });
      throw new Error('Invalid token');
    }
  }

  /**
   * Middleware to authenticate admin requests
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async authenticateAdmin(req, res, next) {
    try {
      // Check for Bearer token in Authorization header
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        // Check for API key as fallback
        const apiKey = req.headers['x-api-key'] || req.query.api_key;
        
        if (apiKey) {
          return AuthMiddleware.authenticateApiKey(req, res, next);
        }
        
        logger.warn('Admin access attempt without authentication', {
          ip: req.ip,
          path: req.path
        });
        
        return res.status(401).json({
          error: 'Authentication required',
          message: 'Please provide a valid Bearer token or API key'
        });
      }

      // Extract and verify token
      const token = authHeader.substring(7); // Remove 'Bearer ' prefix
      const decoded = AuthMiddleware.verifyToken(token);
      
      // Check if user has admin role
      if (decoded.role !== 'admin') {
        logger.warn('Non-admin token used for admin endpoint', {
          userId: decoded.userId,
          role: decoded.role,
          path: req.path
        });
        
        return res.status(403).json({
          error: 'Insufficient permissions',
          message: 'Admin role required'
        });
      }

      // Attach user info to request
      req.user = decoded;
      
      // Audit successful admin access
      await AuditLog.log(decoded.userId, AUDIT_EVENTS.ADMIN_ACCESS, {
        path: req.path,
        method: req.method,
        ip: req.ip
      });
      
      next();
    } catch (error) {
      logger.error('Authentication error', {
        error: error.message,
        path: req.path
      });
      
      return res.status(401).json({
        error: 'Authentication failed',
        message: error.message
      });
    }
  }

  /**
   * Middleware to authenticate using API key
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async authenticateApiKey(req, res, next) {
    try {
      const apiKey = req.headers['x-api-key'] || req.query.api_key;
      const validApiKeys = (process.env.ADMIN_API_KEYS || '').split(',').filter(Boolean);
      
      if (!apiKey || !validApiKeys.includes(apiKey)) {
        logger.warn('Invalid API key attempt', {
          ip: req.ip,
          path: req.path,
          keyPrefix: apiKey ? apiKey.substring(0, 8) + '...' : 'none'
        });
        
        return res.status(401).json({
          error: 'Invalid API key',
          message: 'Please provide a valid API key'
        });
      }

      // Create a pseudo-user object for API key authentication
      req.user = {
        userId: 'api-key-user',
        role: 'admin',
        authMethod: 'api-key'
      };
      
      // Audit API key access
      await AuditLog.log('api-key-user', AUDIT_EVENTS.API_KEY_ACCESS, {
        path: req.path,
        method: req.method,
        ip: req.ip,
        keyPrefix: apiKey.substring(0, 8) + '...'
      });
      
      next();
    } catch (error) {
      logger.error('API key authentication error', {
        error: error.message,
        path: req.path
      });
      
      return res.status(401).json({
        error: 'Authentication failed',
        message: error.message
      });
    }
  }

  /**
   * Middleware to authenticate regular user requests
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async authenticateUser(req, res, next) {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'Please provide a valid Bearer token'
        });
      }

      const token = authHeader.substring(7);
      const decoded = AuthMiddleware.verifyToken(token);
      
      // Attach user info to request
      req.user = decoded;
      
      next();
    } catch (error) {
      logger.error('User authentication error', {
        error: error.message,
        path: req.path
      });
      
      return res.status(401).json({
        error: 'Authentication failed',
        message: error.message
      });
    }
  }

  /**
   * Generate an admin login endpoint (for initial setup)
   * This should be removed or secured in production
   */
  static async handleAdminLogin(req, res) {
    try {
      const { username, password } = req.body;
      
      // In production, validate against database
      const adminUsername = process.env.ADMIN_USERNAME || 'admin';
      const adminPassword = process.env.ADMIN_PASSWORD || 'change-this-password';
      
      if (username !== adminUsername || password !== adminPassword) {
        logger.warn('Failed admin login attempt', {
          username,
          ip: req.ip
        });
        
        return res.status(401).json({
          error: 'Invalid credentials'
        });
      }

      // Generate token
      const token = AuthMiddleware.generateToken({
        userId: 'admin-user',
        username: adminUsername,
        role: 'admin'
      });

      // Audit successful login
      await AuditLog.log('admin-user', AUDIT_EVENTS.ADMIN_LOGIN, {
        ip: req.ip,
        username
      });

      res.json({
        token,
        expiresIn: '24h',
        message: 'Login successful'
      });
    } catch (error) {
      logger.error('Admin login error', { error: error.message });
      res.status(500).json({
        error: 'Login failed',
        message: error.message
      });
    }
  }
}

module.exports = AuthMiddleware;