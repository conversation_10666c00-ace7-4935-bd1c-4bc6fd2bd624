class MotivationalQuotes {
  constructor() {
    this.quotes = {
      general: [
        "Progress is progress, no matter how small.",
        "Every day is a new opportunity to improve.",
        "Consistency is the mother of mastery.",
        "Small steps daily lead to big changes yearly.",
        "Your only competition is who you were yesterday.",
        "Success is the sum of small efforts repeated daily.",
        "Don't break the chain.",
        "Discipline is choosing between what you want now and what you want most.",
        "The secret to getting ahead is getting started.",
        "Excellence is not an act, but a habit."
      ],
      streak: [
        "Every streak starts with day one.",
        "Streaks are built one day at a time.",
        "Your streak is your superpower.",
        "Don't let a bad day break a good streak.",
        "Consistency beats perfection.",
        "The magic happens when you don't give up.",
        "Your streak is proof of your commitment.",
        "Build your empire one day at a time.",
        "Streaks create momentum.",
        "Your future self will thank you for not breaking the chain."
      ],
      perfectDay: [
        "Perfect days are made, not found.",
        "Today you crushed it!",
        "Five for five - that's excellence in action.",
        "You're building the life you want.",
        "Perfect execution leads to perfect results.",
        "This is what winning looks like.",
        "You made today count.",
        "Excellence achieved.",
        "That's how champions do it.",
        "Perfect day, perfect you."
      ],
      recovery: [
        "Comeback stories are the best stories.",
        "It's not about falling down, it's about getting back up.",
        "Champions don't stay down.",
        "Every setback is a setup for a comeback.",
        "Resilience is your strongest muscle.",
        "The best time to start again is now.",
        "Your comeback starts today.",
        "Failure is not final unless you stop trying.",
        "Get back up and show them what you're made of.",
        "Recovery is where legends are born."
      ],
      milestone: [
        "30 days of dedication - incredible!",
        "You've proven you can do anything.",
        "This is just the beginning of your transformation.",
        "30 days stronger, 30 days wiser.",
        "Consistency for 30 days - you're unstoppable.",
        "100 days of discipline - you're a force of nature!",
        "100 days of commitment - that's legendary status.",
        "Your 100-day journey is an inspiration.",
        "100 days of progress - you've changed everything.",
        "Welcome to the 100-day club - elite level achieved!"
      ],
      mastery: [
        "Mastery is a journey, not a destination.",
        "You're becoming the expert version of yourself.",
        "Excellence is your new normal.",
        "Advanced level unlocked - keep pushing.",
        "Your skills are becoming unshakeable.",
        "Expertise earned through daily practice.",
        "You've mastered the art of showing up.",
        "This is what mastery looks like.",
        "Advanced practitioner - level up complete.",
        "Your dedication has made you unstoppable."
      ]
    };
  }

  getRandomQuote(category = 'general') {
    const categoryQuotes = this.quotes[category] || this.quotes.general;
    const randomIndex = Math.floor(Math.random() * categoryQuotes.length);
    return categoryQuotes[randomIndex];
  }

  getQuoteByContext(context) {
    switch (context) {
      case 'perfect_day':
        return this.getRandomQuote('perfectDay');
      case 'long_streak':
        return this.getRandomQuote('streak');
      case 'recovery':
        return this.getRandomQuote('recovery');
      case 'milestone_30':
      case 'milestone_100':
        return this.getRandomQuote('milestone');
      case 'mastery':
        return this.getRandomQuote('mastery');
      default:
        return this.getRandomQuote('general');
    }
  }
}

module.exports = new MotivationalQuotes();