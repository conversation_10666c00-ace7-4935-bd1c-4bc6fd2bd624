#!/usr/bin/env node

/**
 * Utility script to reset a user back to the beginning of the habit onboarding flow
 * Usage: node src/utils/resetUser.js <phone_number>
 * Example: node src/utils/resetUser.js +1234567890
 */

require('dotenv').config();
const User = require('../models/User');
const pool = require('../db/connection');

async function resetUserOnboarding(phone) {
  try {
    // Find user by phone number
    const user = await User.findByPhone(phone);
    
    if (!user) {
      console.error(`❌ User not found with phone: ${phone}`);
      process.exit(1);
    }

    console.log(`📱 Found user: ${user.display_name || 'Unnamed'} (ID: ${user.id})`);
    console.log(`📊 Current status: ${user.status}, State: ${user.current_state}`);
    
    // Reset user to onboarding
    const updatedUser = await User.resetToOnboarding(user.id);
    
    console.log(`✅ User reset successful!`);
    console.log(`📝 New status: ${updatedUser.status}`);
    console.log(`🔄 New state: ${updatedUser.current_state}`);
    console.log(`💬 User will see "Enter habit 1 of 5:" on next message`);
    
  } catch (error) {
    console.error('❌ Error resetting user:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Parse command line arguments
const phone = process.argv[2];

if (!phone) {
  console.log(`
Usage: node src/utils/resetUser.js <phone_number>

Examples:
  node src/utils/resetUser.js +1234567890
  node src/utils/resetUser.js whatsapp:+1234567890

This will:
- Delete all existing habits and habit logs for the user
- Reset user status to ONBOARDING
- Set state to SETTING_HABIT with habitNumber: 1
- User will see "Enter habit 1 of 5:" on next interaction
`);
  process.exit(1);
}

// Run the reset
resetUserOnboarding(phone).catch(console.error);