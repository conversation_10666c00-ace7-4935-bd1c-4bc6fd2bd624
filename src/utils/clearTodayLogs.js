#!/usr/bin/env node

/**
 * Utility script to clear today's habit logs for a user (for testing)
 * Usage: node src/utils/clearTodayLogs.js <phone_number>
 * Example: node src/utils/clearTodayLogs.js +1234567890
 */

require('dotenv').config();
const pool = require('../db/connection');

async function clearTodayLogs(phone) {
  let client;
  try {
    client = await pool.connect();
    
    // Find user
    const userResult = await client.query(
      'SELECT id, display_name FROM users WHERE phone = $1',
      [phone]
    );
    
    if (userResult.rows.length === 0) {
      console.error(`❌ User not found with phone: ${phone}`);
      process.exit(1);
    }
    
    const user = userResult.rows[0];
    console.log(`📱 Found user: ${user.display_name || 'Unnamed'} (ID: ${user.id})`);
    
    // Delete today's logs
    const deleteResult = await client.query(
      'DELETE FROM habit_logs WHERE user_id = $1 AND log_date = CURRENT_DATE',
      [user.id]
    );
    
    console.log(`🗑️  Cleared ${deleteResult.rowCount} habit log(s) for today`);
    
    // Show current habit status
    const habitsResult = await client.query(
      `SELECT h.habit_number, h.habit_name, hl.completed
       FROM habits h
       LEFT JOIN habit_logs hl ON h.id = hl.habit_id AND hl.log_date = CURRENT_DATE
       WHERE h.user_id = $1
       ORDER BY h.habit_number`,
      [user.id]
    );
    
    console.log('\n📋 Current habit status for today:');
    habitsResult.rows.forEach(habit => {
      const status = habit.completed === null ? '⚠️' : 
                     habit.completed === true ? '✅' : '❌';
      console.log(`  ${status} ${habit.habit_number}. ${habit.habit_name}`);
    });
    
    console.log('\n✅ All habits now show ⚠️ status (unlogged) for testing');
    
  } catch (error) {
    console.error('❌ Error clearing logs:', error.message);
    process.exit(1);
  } finally {
    if (client) client.release();
    await pool.end();
  }
}

// Parse command line arguments
const phone = process.argv[2];

if (!phone) {
  console.log(`
Usage: node src/utils/clearTodayLogs.js <phone_number>

Examples:
  node src/utils/clearTodayLogs.js +1234567890
  node src/utils/clearTodayLogs.js whatsapp:+1234567890

This will:
- Clear all of today's habit logs for the specified user
- Reset all habits to show ⚠️ status (unlogged)
- Useful for testing the "blank slate" main menu
`);
  process.exit(1);
}

// Run the clear
clearTodayLogs(phone).catch(console.error);