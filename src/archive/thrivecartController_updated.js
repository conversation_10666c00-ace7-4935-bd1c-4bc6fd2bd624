const crypto = require('crypto');
const pool = require('../db/connection');
const logger = require('../config/logger');
const emailService = require('../services/emailService');
const { generateAccessCode, generateAffiliateCode } = require('../utils/codeGenerator');

class ThriveCartController {
  constructor() {
    this.testMode = process.env.PAYMENT_TEST_MODE === 'true';
    this.webhookSecret = process.env.THRIVECART_WEBHOOK_SECRET;
    
    // Product configuration for 4 separate products
    this.products = {
      'annual': {
        name: 'Annual',
        price: 39.99,
        subscriptionType: 'yearly',
        billingFrequencyDays: 365,
        isAffiliate: true,
        supportsBumps: true
      },
      'monthly': {
        name: 'Monthly',
        price: 5.99,
        subscriptionType: 'monthly',
        billingFrequencyDays: 30,
        isAffiliate: false,
        supportsBumps: false
      },
      'weekly': {
        name: 'Weekly',
        price: 2.99,
        subscriptionType: 'weekly',
        billingFrequencyDays: 7,
        isAffiliate: false,
        supportsBumps: false
      },
      'lifetime': {
        name: 'Lifetime',
        price: 99.99,
        subscriptionType: 'lifetime',
        billingFrequencyDays: null,
        isAffiliate: true,
        supportsBumps: false
      }
    };
    
    // Bump order configuration
    this.bumps = {
      'friend': {
        name: 'Friend Bump',
        price: 20.00,
        additionalCodes: 1,  // 1 extra code = 2 total
        description: 'Get an extra code for a friend'
      },
      'tribe': {
        name: 'Tribe Bump',
        price: 60.00,
        additionalCodes: 3,  // 3 extra codes = 4 total
        description: 'Get 3 extra codes for your tribe'
      }
    };
  }

  /**
   * Main webhook handler for ThriveCart events
   */
  async handleWebhook(req, res) {
    try {
      const payload = req.body;
      
      // Log webhook event
      await this.logWebhookEvent(payload, req.headers);

      // Verify ThriveCart secret if provided
      if (!this.verifyThriveCartSecret(payload)) {
        logger.error('Invalid or missing ThriveCart secret');
        return res.status(401).json({ error: 'Invalid secret' });
      }

      // Process different event types
      const eventType = payload.event;
      logger.info('Processing ThriveCart webhook', { eventType, testMode: this.testMode });

      switch (eventType) {
        case 'order.success':
        case 'order.purchase':
          await this.handleOrderSuccess(payload);
          break;
        case 'order.subscription_payment':
        case 'order.rebill_success':
          await this.handleSubscriptionPayment(payload);
          break;
        case 'order.refund':
          await this.handleRefund(payload);
          break;
        case 'order.subscription_cancelled':
        case 'order.subscription_paused':
          await this.handleSubscriptionCancelled(payload);
          break;
        case 'order.subscription_resumed':
          await this.handleSubscriptionResumed(payload);
          break;
        default:
          logger.info('Unhandled ThriveCart event type', { eventType });
      }

      res.status(200).json({ received: true });
    } catch (error) {
      logger.error('ThriveCart webhook error', { error: error.message, stack: error.stack });
      res.status(500).json({ error: 'Webhook processing failed' });
    }
  }

  /**
   * Verify ThriveCart secret parameter
   */
  verifyThriveCartSecret(payload) {
    const providedSecret = payload.thrivecart_secret;
    
    if (!this.webhookSecret) {
      logger.warn('ThriveCart webhook secret not configured - skipping verification');
      return true;
    }
    
    return providedSecret === this.webhookSecret;
  }

  /**
   * Identify product from webhook payload using product name instead of pricing
   */
  identifyProduct(payload) {
    const productName = (payload.product?.name || payload.product_name || '').toLowerCase();
    
    // Check for specific product identifiers in the name
    if (productName.includes('annual') || productName.includes('yearly')) {
      return 'annual';
    } else if (productName.includes('lifetime') || productName.includes('founder')) {
      return 'lifetime';
    } else if (productName.includes('monthly')) {
      return 'monthly';
    } else if (productName.includes('weekly') || productName.includes('7 day')) {
      return 'weekly';
    }
    
    // Fallback: try to determine from payment plan name
    const planName = (payload.order?.charges?.[0]?.payment_plan_name || '').toLowerCase();
    
    if (planName.includes('annual') || planName.includes('yearly')) {
      return 'annual';
    } else if (planName.includes('lifetime') || planName.includes('founder')) {
      return 'lifetime';
    } else if (planName.includes('monthly')) {
      return 'monthly';
    } else if (planName.includes('weekly')) {
      return 'weekly';
    }
    
    // Default to monthly if unable to determine
    logger.warn('Unable to identify product type, defaulting to monthly', {
      productName,
      planName
    });
    return 'monthly';
  }

  /**
   * Parse bump orders from the charges array
   */
  parseBumpOrders(payload) {
    const bumps = [];
    
    // Check the charges array for bump orders
    if (payload.order?.charges && Array.isArray(payload.order.charges)) {
      for (const charge of payload.order.charges) {
        const chargeName = (charge.name || charge.item_name || '').toLowerCase();
        
        // Check if this is a bump order
        if (chargeName.includes('friend') || chargeName.includes('extra code')) {
          bumps.push({
            type: 'friend',
            price: parseFloat(charge.amount) / 100, // Convert from cents
            quantity: 1
          });
        } else if (chargeName.includes('tribe') || chargeName.includes('multiple codes')) {
          bumps.push({
            type: 'tribe',
            price: parseFloat(charge.amount) / 100,
            quantity: 1
          });
        }
      }
    }
    
    // Alternative: check for bump_orders or upsells in payload
    if (payload.bump_orders && Array.isArray(payload.bump_orders)) {
      for (const bump of payload.bump_orders) {
        const bumpName = (bump.name || '').toLowerCase();
        
        if (bumpName.includes('friend')) {
          bumps.push({
            type: 'friend',
            price: parseFloat(bump.price || 20),
            quantity: bump.quantity || 1
          });
        } else if (bumpName.includes('tribe')) {
          bumps.push({
            type: 'tribe',
            price: parseFloat(bump.price || 60),
            quantity: bump.quantity || 1
          });
        }
      }
    }
    
    return bumps;
  }

  /**
   * Generate multiple access codes based on bump orders
   */
  async generateCodesWithBumps(productType, bumps) {
    const codes = [];
    
    // Generate the main access code
    const mainCode = await generateAccessCode();
    codes.push({
      code: mainCode,
      type: 'primary',
      description: 'Main access code'
    });
    
    // Generate additional codes for bump orders
    for (const bump of bumps) {
      const bumpConfig = this.bumps[bump.type];
      if (bumpConfig) {
        for (let i = 0; i < bumpConfig.additionalCodes; i++) {
          const additionalCode = await generateAccessCode();
          codes.push({
            code: additionalCode,
            type: 'bump',
            bumpType: bump.type,
            description: `${bumpConfig.name} - Code ${i + 1}`
          });
        }
      }
    }
    
    return codes;
  }

  /**
   * Handle successful order (new purchase)
   */
  async handleOrderSuccess(payload) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Extract customer data
      const customerData = payload.customer || {};
      const customer = {
        email: customerData.email || payload.email || payload.customer_email,
        customer_id: customerData.id || payload.customer_id,
        name: customerData.name || payload.customer_name || `${customerData.first_name || ''} ${customerData.last_name || ''}`.trim()
      };
      
      if (!customer.email) {
        throw new Error('Customer email is required but was not provided in webhook payload');
      }
      
      // Identify the product
      const productType = this.identifyProduct(payload);
      const productConfig = this.products[productType];
      
      // Parse bump orders
      const bumps = this.parseBumpOrders(payload);
      
      // Calculate total amount
      const orderData = payload.order || {};
      let totalAmount = 0;
      if (orderData.total_str) {
        totalAmount = parseFloat(orderData.total_str);
      } else if (orderData.total) {
        totalAmount = parseFloat(orderData.total) / 100;
      } else {
        totalAmount = parseFloat(payload.order_total || payload.total || productConfig.price);
      }
      
      const order = {
        order_id: orderData.id || payload.order_id || payload.transaction_id || `TC_${Date.now()}_${customer.email}`,
        total: totalAmount,
        currency: payload.currency || orderData.currency || 'USD'
      };
      
      // Extract subscription details
      const subscriptionDetails = {
        subscriptionType: productConfig.subscriptionType,
        nextBillingDate: null,
        billingFrequencyDays: productConfig.billingFrequencyDays,
        paymentPlanId: payload.order?.charges?.[0]?.payment_plan_id || null,
        paymentPlanName: productConfig.name,
        expiresAt: null
      };
      
      // Calculate expiration for non-lifetime subscriptions
      if (productConfig.subscriptionType !== 'lifetime') {
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + productConfig.billingFrequencyDays);
        subscriptionDetails.expiresAt = expirationDate;
        subscriptionDetails.nextBillingDate = expirationDate;
      }
      
      // Check if user already exists
      const existingUser = await client.query(
        'SELECT * FROM paid_users WHERE email = $1',
        [customer.email]
      );

      let paidUser;
      let allCodes = [];
      
      if (existingUser.rows.length > 0) {
        // Update existing user
        paidUser = existingUser.rows[0];
        
        // Generate codes with bumps
        allCodes = await this.generateCodesWithBumps(productType, bumps);
        
        // Update user record
        const updateResult = await client.query(
          `UPDATE paid_users 
           SET status = 'active',
               subscription_type = $1,
               amount_paid = $2,
               next_billing_date = $3,
               billing_frequency_days = $4,
               payment_plan_id = $5,
               payment_plan_name = $6,
               expires_at = $7,
               updated_at = NOW()
           WHERE email = $8
           RETURNING *`,
          [
            subscriptionDetails.subscriptionType,
            order.total,
            subscriptionDetails.nextBillingDate,
            subscriptionDetails.billingFrequencyDays,
            subscriptionDetails.paymentPlanId,
            subscriptionDetails.paymentPlanName,
            subscriptionDetails.expiresAt,
            customer.email
          ]
        );
        paidUser = updateResult.rows[0];
        
      } else {
        // Generate codes with bumps
        allCodes = await this.generateCodesWithBumps(productType, bumps);
        
        // Get primary code
        const primaryCode = allCodes.find(c => c.type === 'primary').code;
        
        // Generate affiliate code for eligible products
        const affiliateCode = productConfig.isAffiliate ? await generateAffiliateCode() : null;

        // Create new paid user record
        const paidUserResult = await client.query(
          `INSERT INTO paid_users (
            email, access_code, subscription_type, subscription_id,
            customer_id, amount_paid, currency, status,
            is_affiliate, affiliate_code, paid_at, expires_at,
            next_billing_date, billing_frequency_days,
            payment_plan_id, payment_plan_name,
            fastspring_order_id, test_mode
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
          RETURNING *`,
          [
            customer.email,
            primaryCode,
            subscriptionDetails.subscriptionType,
            order.order_id,
            customer.customer_id || customer.email,
            order.total,
            order.currency || 'USD',
            'active',
            productConfig.isAffiliate,
            affiliateCode,
            new Date(),
            subscriptionDetails.expiresAt,
            subscriptionDetails.nextBillingDate,
            subscriptionDetails.billingFrequencyDays,
            subscriptionDetails.paymentPlanId,
            subscriptionDetails.paymentPlanName,
            order.order_id,
            this.testMode
          ]
        );

        paidUser = paidUserResult.rows[0];
        paidUser.affiliate_code = affiliateCode;
      }

      // Create access code records for all codes
      for (const codeInfo of allCodes) {
        await client.query(
          `INSERT INTO access_codes (code, paid_user_id, is_active, notes)
           VALUES ($1, $2, $3, $4)`,
          [
            codeInfo.code, 
            paidUser.id, 
            true,
            codeInfo.description
          ]
        );
      }

      // Log transaction
      await client.query(
        `INSERT INTO payment_transactions (
          paid_user_id, transaction_id, type, amount, currency,
          fastspring_order_id, transaction_date
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          paidUser.id,
          order.order_id,
          'payment',
          order.total,
          order.currency || 'USD',
          order.order_id,
          new Date()
        ]
      );

      // Queue welcome email with all codes
      await this.queueWelcomeEmailWithCodes(paidUser, allCodes, productConfig, bumps);

      // Mark webhook as processed
      await this.markWebhookProcessed(payload);

      await client.query('COMMIT');
      
      logger.info('Order processed successfully', {
        email: customer.email,
        productType,
        bumps: bumps.length,
        totalCodes: allCodes.length
      });
      
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error processing ThriveCart order', { error: error.message });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Handle subscription payment (renewal)
   */
  async handleSubscriptionPayment(payload) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      const customerData = payload.customer || {};
      const customer = {
        email: customerData.email || payload.email || payload.customer_email
      };
      
      if (!customer.email) {
        logger.error('Customer email missing in subscription payment', { payload });
        throw new Error('Customer email is required');
      }
      
      // Identify product type
      const productType = this.identifyProduct(payload);
      const productConfig = this.products[productType];
      
      const orderData = payload.order || {};
      let totalAmount = 0;
      if (orderData.total_str) {
        totalAmount = parseFloat(orderData.total_str);
      } else if (orderData.total) {
        totalAmount = parseFloat(orderData.total) / 100;
      } else {
        totalAmount = parseFloat(payload.order_total || payload.total || 0);
      }
      
      const order = {
        order_id: orderData.id || payload.order_id || payload.transaction_id || `TC_${Date.now()}_${customer.email}`,
        total: totalAmount,
        currency: payload.currency || orderData.currency || 'USD'
      };
      
      // Calculate new expiration
      let newExpiresAt = null;
      if (productConfig.subscriptionType !== 'lifetime' && productConfig.billingFrequencyDays) {
        newExpiresAt = new Date();
        newExpiresAt.setDate(newExpiresAt.getDate() + productConfig.billingFrequencyDays);
      }
      
      // Update subscription status and extend expiration
      await client.query(
        `UPDATE paid_users 
         SET status = 'active',
             expires_at = $1,
             next_billing_date = $2,
             updated_at = NOW()
         WHERE email = $3`,
        [newExpiresAt, newExpiresAt, customer.email]
      );

      // Get user ID for transaction logging
      const userResult = await client.query(
        'SELECT id, subscription_type FROM paid_users WHERE email = $1',
        [customer.email]
      );

      if (userResult.rows[0]) {
        // Log transaction
        await client.query(
          `INSERT INTO payment_transactions (
            paid_user_id, transaction_id, type, amount, currency,
            fastspring_order_id, transaction_date, extends_expiration
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
          [
            userResult.rows[0].id,
            order.order_id,
            'payment',
            order.total,
            order.currency || 'USD',
            order.order_id,
            new Date(),
            true
          ]
        );
      }

      await client.query('COMMIT');
      
      logger.info('Subscription payment processed - access extended', {
        email: customer.email,
        amount: order.total,
        newExpiresAt,
        productType
      });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error handling subscription payment', { error: error.message });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Handle refund
   */
  async handleRefund(payload) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      const customerData = payload.customer || {};
      const customer = {
        email: customerData.email || payload.email || payload.customer_email
      };
      
      if (!customer.email) {
        logger.error('Customer email missing in refund', { payload });
        throw new Error('Customer email is required');
      }
      
      const orderData = payload.order || {};
      let totalAmount = 0;
      if (orderData.total_str) {
        totalAmount = parseFloat(orderData.total_str);
      } else if (orderData.total) {
        totalAmount = parseFloat(orderData.total) / 100;
      } else {
        totalAmount = parseFloat(payload.order_total || payload.total || 0);
      }
      
      const order = {
        order_id: orderData.id || payload.order_id || payload.transaction_id || `TC_${Date.now()}_${customer.email}`,
        total: totalAmount,
        currency: payload.currency || orderData.currency || 'USD'
      };
      
      // Update user status
      await client.query(
        `UPDATE paid_users 
         SET status = 'expired',
             updated_at = NOW()
         WHERE email = $1`,
        [customer.email]
      );

      // Get user ID
      const userResult = await client.query(
        'SELECT id FROM paid_users WHERE email = $1',
        [customer.email]
      );

      if (userResult.rows[0]) {
        // Log refund transaction
        await client.query(
          `INSERT INTO payment_transactions (
            paid_user_id, transaction_id, type, amount, currency,
            fastspring_order_id, transaction_date
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
          [
            userResult.rows[0].id,
            order.order_id,
            'refund',
            -Math.abs(order.total),
            order.currency || 'USD',
            order.order_id,
            new Date()
          ]
        );

        // Deactivate all access codes for this user
        await client.query(
          `UPDATE access_codes 
           SET is_active = false 
           WHERE paid_user_id = $1`,
          [userResult.rows[0].id]
        );
      }

      await client.query('COMMIT');
      
      logger.info('Refund processed', {
        email: customer.email,
        amount: order.total
      });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error handling refund', { error: error.message });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Handle subscription cancellation
   */
  async handleSubscriptionCancelled(payload) {
    try {
      const customerData = payload.customer || {};
      const customer = {
        email: customerData.email || payload.email || payload.customer_email
      };
      
      if (!customer.email) {
        logger.error('Customer email missing in cancellation', { payload });
        return;
      }
      
      await pool.query(
        `UPDATE paid_users 
         SET status = 'cancelled',
             expires_at = NOW(),
             updated_at = NOW()
         WHERE email = $1`,
        [customer.email]
      );

      logger.info('Subscription cancelled', {
        email: customer.email
      });
    } catch (error) {
      logger.error('Error handling subscription cancellation', { error: error.message });
      throw error;
    }
  }

  /**
   * Handle subscription resumption
   */
  async handleSubscriptionResumed(payload) {
    try {
      const customerData = payload.customer || {};
      const customer = {
        email: customerData.email || payload.email || payload.customer_email
      };
      
      if (!customer.email) {
        logger.error('Customer email missing in resumption', { payload });
        return;
      }
      
      await pool.query(
        `UPDATE paid_users 
         SET status = 'active',
             expires_at = NULL,
             updated_at = NOW()
         WHERE email = $1`,
        [customer.email]
      );

      logger.info('Subscription resumed', {
        email: customer.email
      });
    } catch (error) {
      logger.error('Error handling subscription resumption', { error: error.message });
      throw error;
    }
  }

  /**
   * Queue welcome email with multiple access codes
   */
  async queueWelcomeEmailWithCodes(paidUser, codes, productConfig, bumps) {
    try {
      const templateData = {
        accessCodes: codes,
        primaryCode: codes.find(c => c.type === 'primary').code,
        subscriptionType: paidUser.subscription_type,
        subscriptionTypeDisplay: productConfig.name,
        pricingText: productConfig.subscriptionType === 'lifetime' 
          ? 'One-time payment' 
          : `$${productConfig.price}/${productConfig.subscriptionType === 'yearly' ? 'year' : productConfig.subscriptionType === 'monthly' ? 'month' : 'week'}`,
        amountPaid: paidUser.amount_paid,
        affiliateCode: paidUser.affiliate_code,
        botPhone: process.env.TWILIO_PHONE_NUMBER || '+19035155547',
        isAffiliate: productConfig.isAffiliate,
        isLifetime: productConfig.subscriptionType === 'lifetime',
        hasBumps: bumps.length > 0,
        bumps: bumps.map(b => ({
          type: b.type,
          name: this.bumps[b.type].name,
          codesCount: this.bumps[b.type].additionalCodes + 1
        }))
      };

      // Use enhanced template for multiple codes
      const template = codes.length > 1 ? 'welcome_multi_code' : 'welcome_dynamic';
      
      await pool.query(
        `INSERT INTO email_queue (to_email, subject, template, template_data, priority)
         VALUES ($1, $2, $3, $4, $5)`,
        [
          paidUser.email,
          null,  // Let the email template provide the subject
          template,
          JSON.stringify(templateData),
          1
        ]
      );

      // Process email queue immediately
      await emailService.processQueue();
    } catch (error) {
      logger.error('Error queueing welcome email', { error: error.message });
    }
  }

  /**
   * Log webhook event for auditing
   */
  async logWebhookEvent(payload, headers) {
    try {
      await pool.query(
        `INSERT INTO webhook_events (event_type, payload, headers, source, test_mode)
         VALUES ($1, $2, $3, $4, $5)`,
        [
          payload.event || 'unknown',
          JSON.stringify(payload),
          JSON.stringify(headers),
          'thrivecart',
          this.testMode
        ]
      );
    } catch (error) {
      logger.error('Error logging webhook event', { error: error.message });
    }
  }

  /**
   * Mark webhook as processed
   */
  async markWebhookProcessed(payload) {
    try {
      const eventId = payload.order?.order_id || payload.event_id;
      await pool.query(
        `UPDATE webhook_events 
         SET processed = true, processed_at = NOW()
         WHERE payload->>'order_id' = $1 OR event_id = $1`,
        [eventId]
      );
    } catch (error) {
      logger.error('Error marking webhook processed', { error: error.message });
    }
  }

  /**
   * Test endpoint to simulate webhook events
   */
  async testWebhook(req, res) {
    if (!this.testMode) {
      return res.status(403).json({ error: 'Test mode not enabled' });
    }

    try {
      const { email, productType = 'monthly', includeBumps = [] } = req.body;

      // Build test charges array based on bumps
      const charges = [{
        name: this.products[productType].name,
        amount: this.products[productType].price * 100,
        payment_plan_name: this.products[productType].name
      }];
      
      // Add bump charges
      for (const bumpType of includeBumps) {
        if (this.bumps[bumpType]) {
          charges.push({
            name: this.bumps[bumpType].name,
            amount: this.bumps[bumpType].price * 100
          });
        }
      }
      
      // Calculate total
      const total = charges.reduce((sum, charge) => sum + charge.amount, 0);

      const testPayload = {
        event: 'order.success',
        customer: {
          customer_id: `CUST-${Date.now()}`,
          email: email || '<EMAIL>',
          name: 'Test User'
        },
        order: {
          order_id: `TEST-${Date.now()}`,
          total: total,
          total_str: (total / 100).toFixed(2),
          currency: 'USD',
          charges: charges
        },
        product: {
          product_name: this.products[productType].name
        },
        product_name: this.products[productType].name
      };

      // Process as regular webhook
      req.body = testPayload;
      await this.handleWebhook(req, res);
    } catch (error) {
      logger.error('Test webhook error', { error: error.message });
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = new ThriveCartController();