-- Migration to add proper subscription tracking fields
-- Date: 2025-08-23
-- Purpose: Track subscription durations and expiration dates from ThriveCart webhooks

BEGIN;

-- 1. Add new columns to paid_users table
ALTER TABLE paid_users 
ADD COLUMN IF NOT EXISTS next_billing_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS billing_frequency_days INTEGER,
ADD COLUMN IF NOT EXISTS payment_plan_id VARCHAR(50),
ADD COLUMN IF NOT EXISTS payment_plan_name VARCHAR(255);

-- 2. Update subscription_type constraint to include 'weekly'
ALTER TABLE paid_users 
DROP CONSTRAINT IF EXISTS paid_users_subscription_type_check;

ALTER TABLE paid_users 
ADD CONSTRAINT paid_users_subscription_type_check 
CHECK (subscription_type IN ('weekly', 'monthly', 'yearly'));

-- 3. Create index for expires_at for faster queries
CREATE INDEX IF NOT EXISTS idx_paid_users_expires_at 
ON paid_users(expires_at) 
WHERE expires_at IS NOT NULL;

-- 4. Update existing users to have proper expiration dates based on their subscription type
-- Set expiration dates for existing active users (gives them benefit of doubt with full period)
UPDATE paid_users 
SET expires_at = CASE 
    WHEN subscription_type = 'yearly' THEN paid_at + INTERVAL '365 days'
    WHEN subscription_type = 'monthly' THEN paid_at + INTERVAL '30 days'
    ELSE paid_at + INTERVAL '7 days'
END,
billing_frequency_days = CASE
    WHEN subscription_type = 'yearly' THEN 365
    WHEN subscription_type = 'monthly' THEN 30
    ELSE 7
END
WHERE status = 'active' AND expires_at IS NULL;

-- 5. Add column to track subscription renewals
ALTER TABLE payment_transactions
ADD COLUMN IF NOT EXISTS extends_expiration BOOLEAN DEFAULT FALSE;

-- 6. Drop existing view if it exists and recreate
DROP VIEW IF EXISTS active_subscriptions;

CREATE VIEW active_subscriptions AS
SELECT 
    id,
    email,
    phone,
    access_code,
    subscription_type,
    status,
    expires_at,
    CASE 
        WHEN expires_at IS NULL THEN TRUE
        WHEN expires_at > NOW() THEN TRUE
        ELSE FALSE
    END as is_active,
    CASE
        WHEN expires_at IS NULL THEN NULL
        ELSE EXTRACT(EPOCH FROM (expires_at - NOW())) / 86400
    END as days_remaining
FROM paid_users
WHERE status IN ('active', 'cancelled');

-- 7. Add comment for documentation
COMMENT ON TABLE paid_users IS 'Stores paid user subscriptions with proper duration tracking from ThriveCart webhooks';
COMMENT ON COLUMN paid_users.next_billing_date IS 'Next recurring billing date from ThriveCart future_charges';
COMMENT ON COLUMN paid_users.billing_frequency_days IS 'Billing frequency in days (7=weekly, 30=monthly, 365=yearly)';
COMMENT ON COLUMN paid_users.expires_at IS 'Calculated expiration date for subscription access';
COMMENT ON COLUMN paid_users.payment_plan_id IS 'ThriveCart payment plan ID for tracking subscription type';
COMMENT ON COLUMN paid_users.payment_plan_name IS 'Human-readable payment plan name from ThriveCart';

COMMIT;

-- Verification queries
SELECT 
    subscription_type, 
    COUNT(*) as count,
    COUNT(expires_at) as with_expiration
FROM paid_users 
GROUP BY subscription_type;

SELECT 
    id, 
    email, 
    subscription_type, 
    expires_at,
    billing_frequency_days
FROM paid_users 
WHERE status = 'active' 
LIMIT 5;