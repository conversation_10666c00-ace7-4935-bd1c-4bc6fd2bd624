#!/bin/bash

# Reset user to clean slate for testing
# Usage: ./reset-user.sh [phone_number]

PHONE="${1:-+10000000000}"

echo "🔄 Resetting user with phone: $PHONE"
echo ""

PGPASSWORD=postgres psql -U postgres -h localhost -d lockin <<EOF
-- Reset user state to MAIN_MENU
UPDATE users 
SET current_state = 'MAIN_MENU', 
    session_context = '{}',
    status = 'ACTIVE'
WHERE phone = '$PHONE';

-- Clear today's habit logs
DELETE FROM habit_logs 
WHERE user_id = (SELECT id FROM users WHERE phone = '$PHONE') 
AND log_date = CURRENT_DATE;

-- Show user status
SELECT phone, display_name, current_state, status 
FROM users 
WHERE phone = '$PHONE';

-- Show today's logs count
SELECT COUNT(*) as "Today's Logs" 
FROM habit_logs 
WHERE user_id = (SELECT id FROM users WHERE phone = '$PHONE') 
AND log_date = CURRENT_DATE;
EOF

echo ""
echo "✅ User reset complete - ready for testing with clean slate!"