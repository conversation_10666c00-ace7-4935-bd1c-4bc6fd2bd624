#!/usr/bin/env node

require('dotenv').config();
const pool = require('../../src/db/connection');

async function monitorPurchase() {
  try {
    console.log('🔍 MONITORING FOR NEW PURCHASE...');
    console.log('Make your purchase now. I will detect it immediately.');
    
    // Get the current highest email ID
    const currentResult = await pool.query('SELECT MAX(id) as max_id FROM email_queue');
    const currentMaxId = currentResult.rows[0].max_id || 0;
    
    console.log(`Current max email ID: ${currentMaxId}`);
    console.log('Waiting for new purchase...');
    
    // Poll every 2 seconds for new emails
    const pollInterval = setInterval(async () => {
      try {
        const newResult = await pool.query('SELECT * FROM email_queue WHERE id > $1 ORDER BY id DESC LIMIT 1', [currentMaxId]);
        
        if (newResult.rows.length > 0) {
          const newEmail = newResult.rows[0];
          
          console.log('\n🚨 NEW PURCHASE DETECTED!');
          console.log('═'.repeat(60));
          console.log('Email ID:', newEmail.id);
          console.log('To:', newEmail.to_email);
          console.log('Template:', newEmail.template);
          console.log('Status:', newEmail.status);
          console.log('Created:', newEmail.created_at);
          
          console.log('\n📋 TEMPLATE DATA:');
          console.log(JSON.stringify(newEmail.template_data, null, 2));
          
          // Generate the content with this exact data
          const emailService = require('../../src/services/emailService');
          const templateFunc = emailService.templates[newEmail.template];
          const { subject, html, text } = templateFunc(newEmail.template_data);
          
          console.log('\n📊 GENERATED CONTENT:');
          console.log('Subject:', subject);
          console.log('HTML length:', html.length);
          console.log('Text length:', text.length);
          
          // Check step 5 content
          const step5Index = html.indexOf('Start building unstoppable habits!');
          if (step5Index !== -1) {
            const afterStep5 = html.substring(step5Index + 'Start building unstoppable habits!'.length);
            console.log('Characters after step 5:', afterStep5.length);
            
            // Check for footer elements
            const footerChecks = [
              { name: 'Rich signature', check: afterStep5.includes('Let\\'s Lock In') && afterStep5.includes('Rich') },
              { name: 'Affiliate section', check: afterStep5.includes('💰 Earn with Lock In') },
              { name: 'Social links', check: afterStep5.includes('@richvieren') },
              { name: 'Copyright', check: afterStep5.includes('© 2025 Lock In') }
            ];
            
            console.log('\n🔍 FOOTER CONTENT CHECK:');
            footerChecks.forEach(({ name, check }) => {
              console.log(`  ${check ? '✅' : '❌'} ${name}`);
            });
            
            const allGood = footerChecks.every(({ check }) => check);
            console.log(`\n${allGood ? '🎉 CONTENT LOOKS COMPLETE!' : '❌ CONTENT IS BROKEN!'}`);
          } else {
            console.log('❌ Step 5 text not found!');
          }
          
          // Save the exact content
          require('fs').writeFileSync(`/var/www/lockin/customer-purchase-${newEmail.id}.html`, html);
          console.log(`\n💾 Saved to: customer-purchase-${newEmail.id}.html`);
          
          console.log('\n🎯 NOW CHECK YOUR EMAIL AND COMPARE TO GENERATED CONTENT');
          console.log('If email is truncated but generated content is complete,');
          console.log('then the issue is in SMTP/email delivery, not templates.');
          
          clearInterval(pollInterval);
          await pool.end();
          process.exit(0);
        }
        
      } catch (error) {
        console.error('Poll error:', error.message);
      }
    }, 2000);
    
    // Timeout after 5 minutes
    setTimeout(() => {
      console.log('\\n⏰ Timeout - no purchase detected in 5 minutes');
      clearInterval(pollInterval);
      pool.end();
      process.exit(0);
    }, 300000);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    await pool.end();
  }
}

monitorPurchase().catch(console.error);