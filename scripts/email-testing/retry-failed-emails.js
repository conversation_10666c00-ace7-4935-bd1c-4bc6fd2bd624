const { Client } = require('pg');
const emailService = require('../../src/services/emailService');

async function retryFailedEmails() {
  const client = new Client({
    host: 'localhost',
    database: 'lockin',
    user: 'postgres',
    password: 'postgres'
  });

  try {
    await client.connect();
    
    // Reset failed emails to pending so they can be retried
    const result = await client.query(
      `UPDATE email_queue 
       SET status = 'pending', 
           retry_count = 0,
           error_message = NULL,
           updated_at = NOW()
       WHERE status = 'failed' 
       AND template IN ('welcome_monthly', 'welcome_yearly')
       RETURNING id, to_email`
    );
    
    console.log(`Reset ${result.rowCount} failed emails to pending status`);
    
    if (result.rowCount > 0) {
      console.log('Emails reset:');
      result.rows.forEach(row => {
        console.log(`  - ID: ${row.id}, Email: ${row.to_email}`);
      });
      
      // Process the email queue immediately
      console.log('\nProcessing email queue...');
      await emailService.processQueue();
      
      // Check status after processing
      const statusCheck = await client.query(
        `SELECT status, COUNT(*) as count 
         FROM email_queue 
         WHERE id IN (${result.rows.map(r => r.id).join(',')})
         GROUP BY status`
      );
      
      console.log('\nEmail processing results:');
      statusCheck.rows.forEach(row => {
        console.log(`  ${row.status}: ${row.count} emails`);
      });
    }
    
  } catch (error) {
    console.error('Error retrying emails:', error.message);
  } finally {
    await client.end();
  }
}

retryFailedEmails();