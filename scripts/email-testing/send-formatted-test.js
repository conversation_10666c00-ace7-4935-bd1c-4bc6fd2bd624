#!/usr/bin/env node

require('dotenv').config();

async function sendFormattedTest() {
  try {
    console.log('📧 SENDING FORMATTED TEST EMAIL...');
    
    const thrivecartController = require('../../src/controllers/thrivecartController');
    
    const webhookPayload = {
      thrivecart_account: 'richvieren',
      event: 'order.success',
      event_id: `FORMATTED_TEST_${Date.now()}`,
      
      customer_email: '<EMAIL>',
      customer_name: 'Formatted Test Customer',
      customer_first_name: 'Formatted',
      customer_last_name: 'Test',
      
      product_name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
      order_total: '3999',
      payment_plan_name: 'Annual subscription (ongoing) ($39.99)',
      
      webhook_charges: [{
        name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
        amount: '3999',
        payment_plan_name: 'Annual subscription (ongoing) ($39.99)'
      }],
      
      thrivecart_secret: 'FUQ2A97V0Q8A'
    };
    
    const req = { body: webhookPayload };
    const res = {
      status: (code) => ({
        json: (data) => res
      })
    };
    
    console.log('⚡ Sending formatted email...');
    
    await thrivecartController.handleWebhook(req, res);
    
    console.log('\n✅ FORMATTED EMAIL SENT!');
    console.log('📧 Check your inbox: <EMAIL>');
    console.log('');
    console.log('🎨 Now with proper formatting:');
    console.log('  ✅ Consistent Arial font');
    console.log('  ✅ Nice spacing and padding');
    console.log('  ✅ Clean sections with borders');
    console.log('  ✅ Highlighted access code box');
    console.log('  ✅ Proper button styling');
    console.log('  ✅ Organized footer');
    console.log('');
    console.log('📏 Still simple and safe - no complex CSS!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

sendFormattedTest().catch(console.error);