#!/usr/bin/env node

require('dotenv').config();

async function sendUltraMinimalTest() {
  try {
    console.log('📧 SENDING ULTRA-MINIMAL TEST EMAIL...');
    
    const thrivecartController = require('../../src/controllers/thrivecartController');
    
    const webhookPayload = {
      thrivecart_account: 'richvieren',
      event: 'order.success',
      event_id: `ULTRA_MINIMAL_${Date.now()}`,
      
      customer_email: '<EMAIL>',
      customer_name: 'Ultra Minimal Test',
      customer_first_name: '<PERSON>',
      customer_last_name: 'Minimal',
      
      product_name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
      order_total: '3999',
      payment_plan_name: 'Annual subscription (ongoing) ($39.99)',
      
      webhook_charges: [{
        name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
        amount: '3999',
        payment_plan_name: 'Annual subscription (ongoing) ($39.99)'
      }],
      
      thrivecart_secret: 'FUQ2A97V0Q8A'
    };
    
    const req = { body: webhookPayload };
    const res = {
      status: (code) => ({
        json: (data) => res
      })
    };
    
    console.log('⚡ Sending ultra-minimal email...');
    
    await thrivecartController.handleWebhook(req, res);
    
    console.log('\n✅ ULTRA-MINIMAL EMAIL SENT!');
    console.log('📧 Check your inbox: <EMAIL>');
    console.log('');
    console.log('🔬 Ultra-minimal version:');
    console.log('  📏 Only 1,327 characters (86% smaller!)');
    console.log('  📝 Just <p> and <strong> tags');
    console.log('  📄 No CSS styling at all');
    console.log('  📦 Minimal HTML structure');
    console.log('  ✅ All content still included');
    console.log('');
    console.log('This should be bulletproof against truncation!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

sendUltraMinimalTest().catch(console.error);