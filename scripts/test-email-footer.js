#!/usr/bin/env node

/**
 * Test actual email generation to verify footer is included
 */

const EmailService = require('../src/services/emailService');

// Test data similar to what would come from the webhook
const testData = {
  subscription_type: 'Annual',
  subscription_price: '$30/year',
  primary_access_code: 'TEST123',
  bonus_codes: ['BONUS1', 'BONUS2'],
  botPhone: '+1234567890'
};

console.log('🧪 Testing email generation...\n');

// Get the template method
const emailService = new EmailService.constructor();
const { subject, html, text } = emailService.getWelcomeDynamicTemplate(testData);

console.log('📧 SUBJECT:', subject);
console.log('\n🌐 HTML FOOTER SECTION (last 2000 chars):');
console.log(html.slice(-2000));

console.log('\n📝 TEXT FOOTER SECTION (last 1000 chars):');
console.log(text.slice(-1000));

console.log('\n✅ Test completed. Check above output for <PERSON>\'s personal footer content.');