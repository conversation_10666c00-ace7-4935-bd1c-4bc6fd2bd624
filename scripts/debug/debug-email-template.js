const emailService = require('../../src/services/emailService');
const thrivecartController = require('../../src/controllers/thrivecartController');

// Test data for each subscription type
const testCases = [
  {
    type: 'weekly',
    accessCode: 'HABIT-TEST-WEEKLY',
    amount: 2.99
  },
  {
    type: 'monthly', 
    accessCode: 'HABIT-TEST-MONTHLY',
    amount: 5.99
  },
  {
    type: 'yearly',
    accessCode: 'HABIT-TEST-YEARLY',
    amount: 39.99,
    affiliateCode: 'AFF-TEST-123'
  },
  {
    type: 'lifetime',
    accessCode: 'HABIT-TEST-LIFETIME',
    amount: 99.99
  }
];

console.log('=== DEBUG EMAIL TEMPLATE CONTENT ===\n');

for (const testCase of testCases) {
  console.log(`--- ${testCase.type.toUpperCase()} SUBSCRIPTION ---`);
  
  const pricingInfo = thrivecartController.getPricingInfo(testCase.type, testCase.amount);
  const templateData = {
    accessCode: testCase.accessCode,
    subscriptionType: testCase.type,
    subscriptionTypeDisplay: thrivecartController.getSubscriptionTypeDisplay(testCase.type),
    pricingText: pricingInfo.text,
    amountPaid: testCase.amount,
    affiliateCode: testCase.affiliateCode || null,
    botPhone: '+19035155547',
    isAffiliate: testCase.type === 'yearly',
    isLifetime: testCase.type === 'lifetime'
  };
  
  console.log('Template Data:', {
    subscriptionType: templateData.subscriptionType,
    subscriptionTypeDisplay: templateData.subscriptionTypeDisplay,
    pricingText: templateData.pricingText,
    isLifetime: templateData.isLifetime,
    isAffiliate: templateData.isAffiliate
  });
  
  const emailTemplate = emailService.templates.welcome_dynamic(templateData);
  
  // Show first 200 characters of text version
  const textPreview = emailTemplate.text.substring(0, 300).replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
  console.log('\nEmail Text Preview:');
  console.log(`"${textPreview}..."`);
  
  // Check if subscription type display appears in text
  const hasSubscriptionType = emailTemplate.text.includes(templateData.subscriptionTypeDisplay);
  console.log(`\nContains "${templateData.subscriptionTypeDisplay}": ${hasSubscriptionType}`);
  
  // Check what subscription-related text actually appears
  const subscriptionLines = emailTemplate.text.split('\n').filter(line => 
    line.toLowerCase().includes('subscription') || line.toLowerCase().includes(testCase.type)
  );
  console.log('Subscription-related lines:');
  subscriptionLines.forEach(line => console.log(`  "${line.trim()}"`));
  
  console.log('\n' + '='.repeat(60) + '\n');
}