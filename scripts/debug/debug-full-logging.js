#!/usr/bin/env node

require('dotenv').config();
const stateMachine = require('../../src/services/stateMachine');
const User = require('../../src/models/User');
const Habit = require('../../src/models/Habit');

async function debugLogging() {
  try {
    const testPhone = '+27646921984';
    console.log('🔍 DEBUGGING FULL LOGGING PROCESS');
    
    // Get user and habits
    const user = await User.findByPhone(testPhone);
    const habits = await Habit.findByUserId(user.id);
    
    console.log('\n📊 User and habits:');
    console.log('User ID:', user.id);
    console.log('Habits:', habits.map(h => `${h.habit_number}: ${h.habit_name} (ID: ${h.id})`));
    
    // Simulate the exact logic from handleLoggingHabits
    const input = '1y 2n 3y';
    console.log(`\n📝 Processing input: "${input}"`);
    
    const today = require('moment-timezone').tz(user.timezone).format('YYYY-MM-DD');
    console.log('Today date:', today);
    
    let logsToCreate = [];
    
    // Check for "1y 2n 3y" format first
    const ynMatches = input.match(/(\d+)([yn])/g);
    if (ynMatches && ynMatches.length > 0) {
      console.log('Found ynMatches:', ynMatches);
      
      // Process "1y 2n 3y" format
      ynMatches.forEach(match => {
        const [, num, yn] = match.match(/(\d+)([yn])/);
        const habitNumber = parseInt(num);
        if (habitNumber >= 1 && habitNumber <= 5) {
          logsToCreate.push({
            habitNumber,
            completed: yn === 'y'
          });
        }
      });
    }
    
    console.log('logsToCreate:', logsToCreate);
    
    if (logsToCreate.length > 0) {
      console.log('\n💾 Applying logs to database:');
      
      // Apply the logs
      for (const logEntry of logsToCreate) {
        const habit = habits.find(h => h.habit_number === logEntry.habitNumber);
        console.log(`  Habit ${logEntry.habitNumber}: ${habit ? 'found' : 'NOT FOUND'} (completed: ${logEntry.completed})`);
        
        if (habit) {
          console.log(`  Logging habit ID ${habit.id} for user ${user.id} on ${today}: ${logEntry.completed}`);
          await Habit.logHabit(user.id, habit.id, today, logEntry.completed);
          console.log(`  ✅ Logged successfully`);
        }
      }
      
      console.log('\n🔍 Checking database after logging:');
      const todayLogs = await Habit.getTodayLogs(user.id, user.timezone);
      todayLogs.forEach(log => {
        const status = log.completed === null ? 'NULL (⚠️)' : 
                       log.completed === true ? 'TRUE (✅)' : 'FALSE (❌)';
        console.log(`  ${log.habit_number}. ${log.habit_name}: ${status}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

debugLogging().catch(console.error);