#!/usr/bin/env node

require('dotenv').config();
const nodemailer = require('nodemailer');
const pool = require('../../src/db/connection');

async function debugActualSMTP() {
  try {
    console.log('🔍 DEBUGGING ACTUAL SMTP CONTENT...');
    
    // Get the most recent email from queue
    const result = await pool.query("SELECT * FROM email_queue WHERE to_email = '<EMAIL>' ORDER BY id DESC LIMIT 1");
    const emailRecord = result.rows[0];
    
    console.log('Email ID:', emailRecord.id);
    console.log('Template:', emailRecord.template);
    
    // Load email service and generate content
    const emailService = require('../../src/services/emailService');
    const templateFunc = emailService.templates[emailRecord.template];
    const templateData = emailRecord.template_data;
    
    const { subject, html, text } = templateFunc.call(emailService, templateData);
    
    console.log('\n📊 GENERATED CONTENT:');
    console.log('HTML length:', html.length);
    
    // Find step 5 position
    const step5Text = 'Start building unstoppable habits!';
    const step5Index = html.indexOf(step5Text);
    console.log('Step 5 position:', step5Index);
    
    if (step5Index !== -1) {
      const afterStep5 = html.substring(step5Index + step5Text.length);
      console.log('Characters after step 5:', afterStep5.length);
      
      // Show first 300 characters after step 5
      console.log('\nFIRST 300 CHARS AFTER STEP 5:');
      console.log('─'.repeat(60));
      console.log(afterStep5.substring(0, 300));
      console.log('─'.repeat(60));
      
      // Show last 300 characters of email
      console.log('\nLAST 300 CHARS OF EMAIL:');
      console.log('─'.repeat(60));
      console.log(html.slice(-300));
      console.log('─'.repeat(60));
    }
    
    // Now send this exact content via SMTP and log what actually gets sent
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_PORT === '465',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      },
      logger: true,  // Enable logging
      debug: true    // Enable debug
    });
    
    console.log('\n📧 SENDING WITH FULL SMTP LOGGING...');
    
    const mailOptions = {
      from: `"DEBUG Lock In" <${process.env.EMAIL_FROM}>`,
      to: '<EMAIL>',
      subject: 'DEBUG - SMTP Content Analysis',
      html: html,
      text: text
    };
    
    console.log('Mail options prepared:');
    console.log('  HTML length:', mailOptions.html.length);
    console.log('  Text length:', mailOptions.text.length);
    
    const sendResult = await transporter.sendMail(mailOptions);
    
    console.log('\n✅ SMTP SEND RESULT:');
    console.log('Message ID:', sendResult.messageId);
    console.log('Response:', sendResult.response);
    
    // Save the exact content that was sent
    require('fs').writeFileSync('/var/www/lockin/smtp-sent-content.html', html);
    console.log('\n💾 Exact SMTP content saved to: smtp-sent-content.html');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
  
  await pool.end();
}

debugActualSMTP().catch(console.error);