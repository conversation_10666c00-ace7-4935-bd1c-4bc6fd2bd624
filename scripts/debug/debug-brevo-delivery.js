#!/usr/bin/env node

require('dotenv').config();
const { createTransport } = require('nodemailer');

async function debugBrevoDelivery() {
  console.log('🔍 BREVO DELIVERY DEBUGGING');
  console.log('=' .repeat(40));
  
  console.log('\n❓ POTENTIAL ISSUES:');
  console.log('1. Sender domain verification in Brevo');
  console.log('2. Emails being filtered as spam');
  console.log('3. Rate limiting or quota issues');
  console.log('4. Authentication/credentials issues');
  
  console.log('\n📧 CURRENT FROM ADDRESS:');
  console.log(`   ${process.env.EMAIL_FROM}`);
  console.log('   ⚠️  Domain: habittracker.com (may not be verified in Brevo)');
  
  console.log('\n🧪 TESTING DIFFERENT FROM ADDRESSES:');
  
  const transporter = createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT),
    secure: false,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  });
  
  const testCases = [
    {
      name: 'Current Config',
      from: `"Lockin Habit Tracker" <${process.env.EMAIL_FROM}>`,
      to: '<EMAIL>'
    },
    {
      name: 'Brevo Default Domain',
      from: `"Lockin Habit Tracker" <<EMAIL>>`,
      to: '<EMAIL>'
    },
    {
      name: 'Plain Sender',
      from: process.env.EMAIL_FROM,
      to: '<EMAIL>'
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n${testCase.name}:`);
    console.log(`   From: ${testCase.from}`);
    console.log(`   To: ${testCase.to}`);
    
    try {
      const info = await transporter.sendMail({
        from: testCase.from,
        to: testCase.to,
        subject: `Brevo Debug Test - ${testCase.name} - ${new Date().toISOString()}`,
        html: `
          <h2>Brevo Debug Test: ${testCase.name}</h2>
          <p>Test sent at: ${new Date().toISOString()}</p>
          <p>From address: ${testCase.from}</p>
          <p>If you receive this email, the sender configuration works!</p>
        `,
        text: `Brevo Debug Test: ${testCase.name}\nSent at: ${new Date().toISOString()}\nFrom: ${testCase.from}`
      });
      
      console.log(`   ✅ Sent: ${info.messageId}`);
      console.log(`   📮 Response: ${info.response}`);
      
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
      if (error.response) {
        console.log(`   📮 SMTP Response: ${error.response}`);
      }
    }
  }
  
  console.log('\n🔍 DELIVERY CHECK:');
  console.log('   Check these inboxes:');
  console.log('   • https://www.mailinator.com/v4/public/inboxes.jsp?to=current-test');
  console.log('   • https://www.mailinator.com/v4/public/inboxes.jsp?to=brevo-domain-test');
  console.log('   • https://www.mailinator.com/v4/public/inboxes.jsp?to=plain-test');
  
  console.log('\n💡 RECOMMENDATIONS:');
  console.log('1. If emails with @smtp-brevo.com work: Use Brevo\'s default domain');
  console.log('2. If no emails are delivered: Check Brevo account status/quota');
  console.log('3. Verify domain authentication in Brevo dashboard');
  console.log('4. Check if emails are in spam folders');
  
  console.log('\n🎯 NEXT STEPS:');
  console.log('1. Wait 2-3 minutes for email delivery');
  console.log('2. Check all test inboxes');
  console.log('3. If Brevo domain works, update config');
  console.log('4. If nothing works, check Brevo account limits');
}

debugBrevoDelivery().catch(console.error);