#!/usr/bin/env node

/**
 * Preview email templates with the updated footer
 */

const { getEmailFooterHTML, getEmailFooterText } = require('../src/templates/emailFooter');

console.log('\n=== EMAIL FOOTER HTML PREVIEW ===\n');
console.log(getEmailFooterHTML());

console.log('\n=== EMAIL FOOTER TEXT PREVIEW ===\n');
console.log(getEmailFooterText());

console.log('\n✅ Email footer template successfully configured with:');
console.log('   - Personal outro from Rich');
console.log('   - Social links (X and Substack)');
console.log('   - 25% affiliate commission offer');
console.log('   - Lock In website and social media links\n');