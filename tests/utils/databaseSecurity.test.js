/**
 * Unit tests for DatabaseSecurity utility
 */

const DatabaseSecurity = require('../../src/utils/databaseSecurity');

describe('DatabaseSecurity', () => {
  describe('validateTableName', () => {
    test('should accept valid table names', () => {
      const validTables = ['users', 'habits', 'audit_logs', 'access_codes'];
      
      validTables.forEach(table => {
        expect(DatabaseSecurity.validateTableName(table)).toBe(table);
      });
    });

    test('should reject invalid table names', () => {
      const invalidTables = ['invalid_table', 'DROP TABLE users', 'users; DROP TABLE', null, undefined, ''];
      
      invalidTables.forEach(table => {
        expect(() => DatabaseSecurity.validateTableName(table)).toThrow();
      });
    });

    test('should normalize table names to lowercase', () => {
      expect(DatabaseSecurity.validateTableName('USERS')).toBe('users');
      expect(DatabaseSecurity.validateTableName('Habits')).toBe('habits');
    });
  });

  describe('validateColumnName', () => {
    test('should accept valid column names', () => {
      const validColumns = ['id', 'user_id', 'phone', 'created_at'];
      
      validColumns.forEach(column => {
        expect(DatabaseSecurity.validateColumnName(column)).toBe(column);
      });
    });

    test('should reject invalid column names', () => {
      const invalidColumns = ['invalid_column', '1=1', 'id; DROP TABLE', null, undefined, ''];
      
      invalidColumns.forEach(column => {
        expect(() => DatabaseSecurity.validateColumnName(column)).toThrow();
      });
    });
  });

  describe('buildDeleteQuery', () => {
    test('should build safe DELETE query', () => {
      const query = DatabaseSecurity.buildDeleteQuery('users', 'id');
      expect(query).toBe('DELETE FROM users WHERE id = $1');
    });

    test('should use default user_id column', () => {
      const query = DatabaseSecurity.buildDeleteQuery('habits');
      expect(query).toBe('DELETE FROM habits WHERE user_id = $1');
    });

    test('should reject invalid table in DELETE query', () => {
      expect(() => DatabaseSecurity.buildDeleteQuery('invalid_table')).toThrow();
    });
  });

  describe('buildSelectQuery', () => {
    test('should build safe SELECT query', () => {
      const query = DatabaseSecurity.buildSelectQuery('users', ['id', 'phone'], 'id');
      expect(query).toBe('SELECT id, phone FROM users WHERE id = $1');
    });

    test('should handle wildcard selection', () => {
      const query = DatabaseSecurity.buildSelectQuery('users', ['*']);
      expect(query).toBe('SELECT * FROM users');
    });

    test('should build query without WHERE clause', () => {
      const query = DatabaseSecurity.buildSelectQuery('users', ['id', 'phone']);
      expect(query).toBe('SELECT id, phone FROM users');
    });

    test('should reject invalid columns in SELECT query', () => {
      expect(() => DatabaseSecurity.buildSelectQuery('users', ['invalid_column'])).toThrow();
    });
  });

  describe('buildUpdateQuery', () => {
    test('should build safe UPDATE query', () => {
      const query = DatabaseSecurity.buildUpdateQuery('users', ['display_name', 'timezone'], 'id');
      expect(query).toBe('UPDATE users SET display_name = $2, timezone = $3 WHERE id = $1');
    });

    test('should reject invalid columns in UPDATE query', () => {
      expect(() => DatabaseSecurity.buildUpdateQuery('users', ['invalid_column'])).toThrow();
    });
  });

  describe('buildInsertQuery', () => {
    test('should build safe INSERT query', () => {
      const query = DatabaseSecurity.buildInsertQuery('users', ['phone', 'display_name', 'timezone']);
      expect(query).toBe('INSERT INTO users (phone, display_name, timezone) VALUES ($1, $2, $3)');
    });

    test('should reject invalid columns in INSERT query', () => {
      expect(() => DatabaseSecurity.buildInsertQuery('users', ['invalid_column'])).toThrow();
    });
  });

  describe('validateParameters', () => {
    test('should accept valid parameters', () => {
      const validParams = [1, '<EMAIL>', 'John Doe', true, false, null];
      expect(DatabaseSecurity.validateParameters(validParams)).toEqual(validParams);
    });

    test('should reject SQL injection attempts in parameters', () => {
      const injectionAttempts = [
        ['SELECT * FROM users'],
        ['1; DROP TABLE users'],
        ['1 OR 1=1'],
        ['admin\' --'],
        ['"; DELETE FROM users; --']
      ];

      injectionAttempts.forEach(params => {
        expect(() => DatabaseSecurity.validateParameters(params)).toThrow();
      });
    });

    test('should reject non-array parameters', () => {
      expect(() => DatabaseSecurity.validateParameters('not an array')).toThrow();
      expect(() => DatabaseSecurity.validateParameters(null)).toThrow();
    });
  });

  describe('isQuerySafe', () => {
    test('should accept safe queries', () => {
      const safeQueries = [
        'SELECT * FROM users WHERE id = $1',
        'UPDATE users SET name = $1 WHERE id = $2',
        'DELETE FROM habits WHERE user_id = $1',
        'INSERT INTO users (phone) VALUES ($1)'
      ];

      safeQueries.forEach(query => {
        expect(DatabaseSecurity.isQuerySafe(query)).toBe(true);
      });
    });

    test('should reject dangerous queries', () => {
      const dangerousQueries = [
        'DROP TABLE users',
        'TRUNCATE TABLE habits',
        'DELETE FROM users WHERE 1=1',
        'UPDATE users SET admin = true WHERE 1=1',
        'ALTER TABLE users DROP COLUMN password'
      ];

      dangerousQueries.forEach(query => {
        expect(DatabaseSecurity.isQuerySafe(query)).toBe(false);
      });
    });
  });

  describe('escapeIdentifier', () => {
    test('should escape identifiers properly', () => {
      expect(DatabaseSecurity.escapeIdentifier('users')).toBe('"users"');
      expect(DatabaseSecurity.escapeIdentifier('user"name')).toBe('"user""name"');
    });

    test('should reject invalid identifiers', () => {
      expect(() => DatabaseSecurity.escapeIdentifier(null)).toThrow();
      expect(() => DatabaseSecurity.escapeIdentifier('')).toThrow();
    });
  });
});