const User = require('../../src/models/User');
const pool = require('../../src/db/connection');

// Mock the database connection
jest.mock('../../src/db/connection');

describe('User Model', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('findByPhone', () => {
    it('should find a user by phone number', async () => {
      const mockUser = {
        id: 1,
        phone: '+1234567890',
        status: 'LOCKED'
      };

      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.findByPhone('+1234567890');

      expect(pool.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE phone = $1',
        ['+1234567890']
      );
      expect(result).toEqual(mockUser);
    });

    it('should return null if user not found', async () => {
      pool.query.mockResolvedValue({ rows: [] });

      const result = await User.findByPhone('+9999999999');

      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    it('should create a new user', async () => {
      const mockUser = {
        id: 1,
        phone: '+1234567890',
        status: 'LOCKED'
      };

      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.create('+1234567890');

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO users'),
        ['+1234567890', 'LOCKED']
      );
      expect(result).toEqual(mockUser);
    });
  });

  describe('findOrCreate', () => {
    it('should return existing user if found', async () => {
      const mockUser = {
        id: 1,
        phone: '+1234567890',
        status: 'ACTIVE'
      };

      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.findOrCreate('+1234567890');

      expect(result).toEqual(mockUser);
      expect(pool.query).toHaveBeenCalledTimes(1); // Only findByPhone called
    });

    it('should create new user if not found', async () => {
      const mockUser = {
        id: 1,
        phone: '+1234567890',
        status: 'LOCKED'
      };

      pool.query
        .mockResolvedValueOnce({ rows: [] }) // findByPhone returns nothing
        .mockResolvedValueOnce({ rows: [mockUser] }); // create returns new user

      const result = await User.findOrCreate('+1234567890');

      expect(result).toEqual(mockUser);
      expect(pool.query).toHaveBeenCalledTimes(2);
    });
  });

  describe('updateState', () => {
    it('should update user state and context', async () => {
      const mockUser = {
        id: 1,
        current_state: 'MAIN_MENU',
        session_context: {}
      };

      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.updateState(1, 'MAIN_MENU', { test: 'data' });

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE users'),
        ['MAIN_MENU', JSON.stringify({ test: 'data' }), 1]
      );
      expect(result).toEqual(mockUser);
    });
  });

  describe('unlockWithCode', () => {
    it('should unlock user with valid code', async () => {
      const mockClient = {
        query: jest.fn(),
        release: jest.fn()
      };

      pool.connect.mockResolvedValue(mockClient);
      
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce({ rows: [{ code: 'TEST123' }] }) // Update access_codes
        .mockResolvedValueOnce({ rows: [{ id: 1, is_unlocked: true }] }) // Update users
        .mockResolvedValueOnce(); // COMMIT

      const result = await User.unlockWithCode(1, 'TEST123');

      expect(result.success).toBe(true);
      expect(result.user).toBeDefined();
      expect(mockClient.release).toHaveBeenCalled();
    });

    it('should fail with invalid code', async () => {
      const mockClient = {
        query: jest.fn(),
        release: jest.fn()
      };

      pool.connect.mockResolvedValue(mockClient);
      
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce({ rows: [] }) // No matching code
        .mockResolvedValueOnce(); // ROLLBACK

      const result = await User.unlockWithCode(1, 'INVALID');

      expect(result.success).toBe(false);
      expect(result.message).toContain('Invalid or expired');
      expect(mockClient.release).toHaveBeenCalled();
    });
  });

  describe('optOut', () => {
    it('should mark user as opted out', async () => {
      const mockUser = {
        id: 1,
        status: 'PAUSED',
        opted_out_at: new Date()
      };

      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.optOut(1);

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('opted_out_at = NOW()'),
        [1]
      );
      expect(result.status).toBe('PAUSED');
    });
  });
});