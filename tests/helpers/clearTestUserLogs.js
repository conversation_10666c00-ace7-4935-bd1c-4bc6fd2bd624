const pool = require('../../src/db/connection');

/**
 * Clear today's habit logs for test users
 * This ensures tests start with a clean slate (all habits showing ⚠️)
 */
async function clearTestUserLogs(phone = '+10000000000') {
  let client;
  try {
    client = await pool.connect();
    
    // Delete today's logs for the test user
    await client.query(
      'DELETE FROM habit_logs WHERE user_id = (SELECT id FROM users WHERE phone = $1) AND log_date = CURRENT_DATE',
      [phone]
    );
    
    return true;
  } catch (error) {
    console.error('Error clearing test logs:', error.message);
    return false;
  } finally {
    if (client) client.release();
  }
}

module.exports = { clearTestUserLogs };