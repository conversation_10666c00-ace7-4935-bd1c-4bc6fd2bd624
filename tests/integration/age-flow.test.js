const { Pool } = require('pg');

// Create pool with explicit credentials
const pool = new Pool({
  host: 'localhost',
  database: 'lockin',
  user: 'postgres',
  password: 'postgres',
  port: 5432
});

async function testAgeFlow() {
  const client = await pool.connect();
  
  try {
    console.log('\n=== TESTING AGE VERIFICATION FLOW (DATABASE) ===\n');
    
    // Test phone number
    const testPhone = '+15559999' + Math.floor(Math.random() * 1000);
    console.log(`Test phone: ${testPhone}`);
    
    // 1. Create a user who needs age verification
    const userResult = await client.query(
      `INSERT INTO users (phone, display_name, current_state, status, created_at, age_verified, consent_given, terms_accepted)
       VALUES ($1, $2, 'AGE_VERIFICATION', 'ACTIVE', NOW(), FALSE, FALSE, FALSE)
       RETURNING *`,
      [testPhone, 'Test User']
    );
    
    const user = userResult.rows[0];
    console.log(`\nCreated user ID: ${user.id}`);
    console.log(`Initial state: ${user.current_state}`);
    console.log(`Age verified: ${user.age_verified}`);
    console.log(`Consent given: ${user.consent_given}`);
    console.log(`Terms accepted: ${user.terms_accepted}`);
    
    // 2. Simulate age verification (what should happen when user types "25")
    console.log('\n--- Simulating age verification (user enters: 25) ---');
    
    await client.query(
      `UPDATE users SET 
       age_verified = TRUE,
       age_verification_date = NOW(),
       current_state = 'PRIVACY_CONSENT'
       WHERE id = $1`,
      [user.id]
    );
    
    const afterAge = await client.query('SELECT * FROM users WHERE id = $1', [user.id]);
    const userAfterAge = afterAge.rows[0];
    console.log(`State after age: ${userAfterAge.current_state}`);
    console.log(`Age verified: ${userAfterAge.age_verified}`);
    
    // 3. Simulate privacy consent (what should happen when user types "yes")
    console.log('\n--- Simulating privacy consent (user enters: yes) ---');
    
    await client.query(
      `UPDATE users SET 
       consent_given = TRUE,
       consent_timestamp = NOW(),
       consent_version = '1.0',
       current_state = 'TERMS_ACCEPTANCE'
       WHERE id = $1`,
      [user.id]
    );
    
    const afterConsent = await client.query('SELECT * FROM users WHERE id = $1', [user.id]);
    const userAfterConsent = afterConsent.rows[0];
    console.log(`State after consent: ${userAfterConsent.current_state}`);
    console.log(`Consent given: ${userAfterConsent.consent_given}`);
    
    // 4. Simulate terms acceptance (what should happen when user types "accept terms")
    console.log('\n--- Simulating terms acceptance (user enters: accept terms) ---');
    
    await client.query(
      `UPDATE users SET 
       terms_accepted = TRUE,
       terms_version = '1.0',
       terms_accepted_date = NOW(),
       current_state = 'MAIN_MENU'
       WHERE id = $1`,
      [user.id]
    );
    
    const afterTerms = await client.query('SELECT * FROM users WHERE id = $1', [user.id]);
    const userAfterTerms = afterTerms.rows[0];
    console.log(`State after terms: ${userAfterTerms.current_state}`);
    console.log(`Terms accepted: ${userAfterTerms.terms_accepted}`);
    
    console.log('\n✅ Flow progression test completed!');
    console.log('\nExpected flow:');
    console.log('1. AGE_VERIFICATION → User enters age');
    console.log('2. PRIVACY_CONSENT → User consents');
    console.log('3. TERMS_ACCEPTANCE → User accepts');
    console.log('4. MAIN_MENU → Ready to use app');
    
    // Clean up
    await client.query('DELETE FROM users WHERE id = $1', [user.id]);
    console.log('\n🗑️ Test user cleaned up');
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

testAgeFlow();