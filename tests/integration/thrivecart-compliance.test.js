require('dotenv').config();
const axios = require('axios');

const WEBHOOK_URL = process.env.BOT_WEBHOOK_URL || 'http://localhost:3000';
const THRIVECART_SECRET = process.env.THRIVECART_WEBHOOK_SECRET || 'test-secret';

async function testHeadRequest() {
  console.log('\n=== Testing HEAD Request ===');
  try {
    const response = await axios.head(`${WEBHOOK_URL}/webhook/thrivecart`);
    console.log('✅ HEAD request successful');
    console.log(`Status: ${response.status}`);
    return response.status === 200;
  } catch (error) {
    console.log('❌ HEAD request failed');
    console.log(`Error: ${error.message}`);
    return false;
  }
}

async function testFormEncodedData() {
  console.log('\n=== Testing Form-Encoded Data ===');
  try {
    const formData = new URLSearchParams({
      event: 'order.success',
      thrivecart_secret: THRIVECART_SECRET,
      customer_email: '<EMAIL>',
      customer_first_name: 'Test',
      customer_last_name: 'User',
      customer_id: 'CUST-123456',
      order_id: `TEST-${Date.now()}`,
      order_total: '30.00',
      order_currency: 'USD',
      product_name: 'Habit Tracker Yearly',
      item_name: 'Habit Tracker Yearly'
    });

    const response = await axios.post(
      `${WEBHOOK_URL}/webhook/thrivecart`,
      formData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    console.log('✅ Form-encoded POST request successful');
    console.log(`Status: ${response.status}`);
    console.log(`Response: ${JSON.stringify(response.data)}`);
    return response.status === 200;
  } catch (error) {
    console.log('❌ Form-encoded POST request failed');
    console.log(`Error: ${error.message}`);
    if (error.response) {
      console.log(`Response status: ${error.response.status}`);
      console.log(`Response data: ${JSON.stringify(error.response.data)}`);
    }
    return false;
  }
}

async function testInvalidSecret() {
  console.log('\n=== Testing Invalid Secret ===');
  try {
    const formData = new URLSearchParams({
      event: 'order.success',
      thrivecart_secret: 'invalid-secret',
      customer_email: '<EMAIL>',
      order_id: `TEST-${Date.now()}`,
      order_total: '30.00'
    });

    const response = await axios.post(
      `${WEBHOOK_URL}/webhook/thrivecart`,
      formData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    console.log('❌ Invalid secret should have been rejected');
    console.log(`Status: ${response.status}`);
    return false;
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Invalid secret correctly rejected');
      return true;
    } else {
      console.log('❌ Unexpected error with invalid secret');
      console.log(`Error: ${error.message}`);
      return false;
    }
  }
}

async function testMonthlySubscription() {
  console.log('\n=== Testing Monthly Subscription ===');
  try {
    const formData = new URLSearchParams({
      event: 'order.success',
      thrivecart_secret: THRIVECART_SECRET,
      customer_email: '<EMAIL>',
      customer_first_name: 'Monthly',
      customer_last_name: 'User',
      order_id: `MONTHLY-${Date.now()}`,
      order_total: '5.00',
      order_currency: 'USD',
      product_name: 'Habit Tracker Monthly',
      item_name: 'Habit Tracker Monthly'
    });

    const response = await axios.post(
      `${WEBHOOK_URL}/webhook/thrivecart`,
      formData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    console.log('✅ Monthly subscription test successful');
    console.log(`Status: ${response.status}`);
    return response.status === 200;
  } catch (error) {
    console.log('❌ Monthly subscription test failed');
    console.log(`Error: ${error.message}`);
    return false;
  }
}

async function runComplianceTests() {
  console.log('🧪 ThriveCart Webhook Compliance Tests');
  console.log('=====================================');
  
  const tests = [
    { name: 'HEAD Request Support', test: testHeadRequest },
    { name: 'Form-Encoded Data Handling', test: testFormEncodedData },
    { name: 'Secret Validation', test: testInvalidSecret },
    { name: 'Monthly Subscription Processing', test: testMonthlySubscription }
  ];

  let passed = 0;
  let total = tests.length;

  for (const { name, test } of tests) {
    const result = await test();
    if (result) {
      passed++;
    }
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait between tests
  }

  console.log('\n📊 Test Results');
  console.log('===============');
  console.log(`Passed: ${passed}/${total}`);
  console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

  if (passed === total) {
    console.log('🎉 All compliance tests passed!');
    process.exit(0);
  } else {
    console.log('⚠️  Some compliance tests failed. Please review the implementation.');
    process.exit(1);
  }
}

// Handle command line arguments
const testType = process.argv[2];

if (testType === 'head') {
  testHeadRequest().then(result => process.exit(result ? 0 : 1));
} else if (testType === 'form') {
  testFormEncodedData().then(result => process.exit(result ? 0 : 1));
} else if (testType === 'secret') {
  testInvalidSecret().then(result => process.exit(result ? 0 : 1));
} else if (testType === 'monthly') {
  testMonthlySubscription().then(result => process.exit(result ? 0 : 1));
} else {
  runComplianceTests();
}