const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  database: 'lockin',
  user: 'postgres',
  password: 'postgres',
  port: 5432
});

async function testSimplifiedFlow() {
  const client = await pool.connect();
  
  try {
    console.log('\n=== TESTING SIMPLIFIED 2-STEP ONBOARDING FLOW ===\n');
    
    // Create a test user
    const testPhone = '+15559998' + Math.floor(Math.random() * 1000);
    
    const userResult = await client.query(
      `INSERT INTO users (phone, display_name, current_state, status, created_at, age_verified, consent_given, terms_accepted)
       VALUES ($1, $2, 'AGE_VERIFICATION', 'ACTIVE', NOW(), FALSE, FALSE, FALSE)
       RETURNING *`,
      [testPhone, 'Test User']
    );
    
    const user = userResult.rows[0];
    console.log(`Test user created: ${user.id}\n`);
    
    console.log('📋 OLD FLOW (4+ steps):');
    console.log('================================');
    console.log('1. Age verification with legal explanation');
    console.log('2. Privacy consent with GDPR details');
    console.log('3. Terms acceptance with payment info');
    console.log('4. Welcome message');
    console.log('5. Finally habit setup\n');
    
    console.log('✨ NEW FLOW (2 steps after age):');
    console.log('================================');
    console.log('1. Age: "Welcome! Please confirm you\'re 18+ to continue. Just type your age."');
    console.log('2. Single consent: "By continuing, you agree to our Terms and Privacy Policy."');
    console.log('3. Direct to habits: "Welcome to Lockin! Let\'s set up your first habit."\n');
    
    console.log('TESTING FLOW:');
    console.log('=============\n');
    
    // Step 1: Age verification
    console.log('Step 1: User enters age "25"');
    await client.query(
      `UPDATE users SET 
       age_verified = TRUE,
       age_verification_date = NOW(),
       current_state = 'COMBINED_CONSENT'
       WHERE id = $1`,
      [user.id]
    );
    
    const afterAge = await client.query(
      'SELECT current_state, age_verified FROM users WHERE id = $1',
      [user.id]
    );
    console.log(`   ✅ State: ${afterAge.rows[0].current_state} (was PRIVACY_CONSENT before)`);
    console.log(`   ✅ Age verified: ${afterAge.rows[0].age_verified}\n`);
    
    // Step 2: Combined consent
    console.log('Step 2: User types "AGREE"');
    await client.query(
      `UPDATE users SET 
       consent_given = TRUE,
       consent_timestamp = NOW(),
       consent_version = '1.0',
       terms_accepted = TRUE,
       terms_version = '1.0',
       terms_accepted_date = NOW(),
       current_state = 'MAIN_MENU'
       WHERE id = $1`,
      [user.id]
    );
    
    const afterConsent = await client.query(
      'SELECT current_state, consent_given, terms_accepted FROM users WHERE id = $1',
      [user.id]
    );
    console.log(`   ✅ State: ${afterConsent.rows[0].current_state} (skipped TERMS_ACCEPTANCE)`);
    console.log(`   ✅ Privacy consent: ${afterConsent.rows[0].consent_given}`);
    console.log(`   ✅ Terms accepted: ${afterConsent.rows[0].terms_accepted}\n`);
    
    console.log('Step 3: User is now at MAIN_MENU ready for habits!\n');
    
    console.log('🎉 IMPROVEMENTS SUMMARY:');
    console.log('========================');
    console.log('✅ Reduced from 4+ steps to 2 steps');
    console.log('✅ Combined privacy + terms into single AGREE');
    console.log('✅ Removed GDPR/CCPA explanations from onboarding');
    console.log('✅ Removed payment details from onboarding');
    console.log('✅ Direct path to habit setup after consent');
    console.log('✅ Legal info still available via TERMS command\n');
    
    console.log('USER EXPERIENCE:');
    console.log('================');
    console.log('BEFORE: Long legal explanations → Multiple YES/NO loops → Finally habits');
    console.log('AFTER: Age → Single AGREE → Start using app!\n');
    
    // Clean up
    await client.query('DELETE FROM users WHERE id = $1', [user.id]);
    console.log('🗑️ Test user cleaned up\n');
    
  } catch (error) {
    console.error('Test failed:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

testSimplifiedFlow();