#!/usr/bin/env node

/**
 * ThriveCart Webhook Testing Script
 * Simulates real ThriveCart webhook payloads
 */

const axios = require('axios');
require('dotenv').config();

const BASE_URL = process.env.BOT_WEBHOOK_URL || 'http://localhost:3001';
const WEBHOOK_ENDPOINT = '/webhook/thrivecart';

// Real ThriveCart webhook payload formats
const webhookPayloads = {
  // Initial purchase - Monthly subscription
  monthlyPurchase: {
    event: 'order.success',
    event_id: 'evt_' + Date.now(),
    thrivecart_account: 'habittracker',
    customer: {
      customer_id: 'cust_' + Math.random().toString(36).substr(2, 9),
      email: '<EMAIL>',
      name: '<PERSON>',
      ip_address: '***********',
      customer_status: 'active'
    },
    customer_address: {
      address: '123 Main St',
      city: 'Austin',
      state: 'TX',
      zip: '78701',
      country: 'US'
    },
    order: {
      order_id: 'ord_' + Date.now(),
      invoice_id: 'inv_' + Date.now(),
      total: 5.00,
      total_str: '$5.00',
      subtotal: 5.00,
      tax: 0.00,
      currency: 'USD',
      payment_method: 'card',
      processor: 'stripe',
      status: 'success'
    },
    product: {
      product_id: 'prod_habit_monthly',
      product_name: 'Habit Tracker Monthly',
      product_type: 'subscription',
      product_price: 5.00
    },
    subscription: {
      subscription_id: 'sub_' + Date.now(),
      status: 'active',
      frequency: 'monthly',
      frequency_num: 1,
      next_payment_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
    },
    affiliate: {
      affiliate_id: null,
      commission: 0
    },
    date: new Date().toISOString(),
    timestamp: Math.floor(Date.now() / 1000)
  },

  // Initial purchase - Yearly subscription
  yearlyPurchase: {
    event: 'order.success',
    event_id: 'evt_yearly_' + Date.now(),
    thrivecart_account: 'habittracker',
    customer: {
      customer_id: 'cust_yearly_' + Math.random().toString(36).substr(2, 9),
      email: '<EMAIL>',
      name: 'Jane Smith',
      ip_address: '***********',
      customer_status: 'active'
    },
    customer_address: {
      address: '456 Oak Ave',
      city: 'San Francisco',
      state: 'CA',
      zip: '94102',
      country: 'US'
    },
    order: {
      order_id: 'ord_yearly_' + Date.now(),
      invoice_id: 'inv_yearly_' + Date.now(),
      total: 30.00,
      total_str: '$30.00',
      subtotal: 30.00,
      tax: 0.00,
      currency: 'USD',
      payment_method: 'card',
      processor: 'stripe',
      status: 'success'
    },
    product: {
      product_id: 'prod_habit_yearly',
      product_name: 'Habit Tracker Yearly',
      product_type: 'subscription',
      product_price: 30.00
    },
    subscription: {
      subscription_id: 'sub_yearly_' + Date.now(),
      status: 'active',
      frequency: 'yearly',
      frequency_num: 1,
      next_payment_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
    },
    affiliate: {
      affiliate_id: null,
      commission: 0
    },
    date: new Date().toISOString(),
    timestamp: Math.floor(Date.now() / 1000)
  },

  // Subscription renewal/rebill
  subscriptionRenewal: {
    event: 'order.rebill_success',
    event_id: 'evt_rebill_' + Date.now(),
    thrivecart_account: 'habittracker',
    customer: {
      customer_id: 'cust_existing',
      email: '<EMAIL>',
      name: 'Existing User',
      customer_status: 'active'
    },
    order: {
      order_id: 'ord_rebill_' + Date.now(),
      invoice_id: 'inv_rebill_' + Date.now(),
      total: 5.00,
      total_str: '$5.00',
      currency: 'USD',
      payment_method: 'card',
      processor: 'stripe',
      status: 'success'
    },
    product: {
      product_id: 'prod_habit_monthly',
      product_name: 'Habit Tracker Monthly',
      product_type: 'subscription'
    },
    subscription: {
      subscription_id: 'sub_existing',
      status: 'active',
      frequency: 'monthly',
      next_payment_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
    },
    date: new Date().toISOString(),
    timestamp: Math.floor(Date.now() / 1000)
  },

  // Failed payment
  paymentFailed: {
    event: 'order.rebill_failed',
    event_id: 'evt_failed_' + Date.now(),
    thrivecart_account: 'habittracker',
    customer: {
      customer_id: 'cust_failed',
      email: '<EMAIL>',
      name: 'Failed Payment',
      customer_status: 'active'
    },
    order: {
      order_id: 'ord_failed_' + Date.now(),
      total: 5.00,
      currency: 'USD',
      status: 'failed',
      error_message: 'Card declined'
    },
    subscription: {
      subscription_id: 'sub_failed',
      status: 'past_due'
    },
    date: new Date().toISOString(),
    timestamp: Math.floor(Date.now() / 1000)
  },

  // Refund
  refund: {
    event: 'order.refund',
    event_id: 'evt_refund_' + Date.now(),
    thrivecart_account: 'habittracker',
    customer: {
      customer_id: 'cust_refund',
      email: '<EMAIL>',
      name: 'Refund Customer'
    },
    order: {
      order_id: 'ord_refund_' + Date.now(),
      original_order_id: 'ord_original_123',
      refund_amount: 5.00,
      refund_amount_str: '$5.00',
      currency: 'USD',
      refund_type: 'full',
      reason: 'Customer request'
    },
    date: new Date().toISOString(),
    timestamp: Math.floor(Date.now() / 1000)
  },

  // Subscription cancellation
  subscriptionCancelled: {
    event: 'order.subscription_cancelled',
    event_id: 'evt_cancel_' + Date.now(),
    thrivecart_account: 'habittracker',
    customer: {
      customer_id: 'cust_cancel',
      email: '<EMAIL>',
      name: 'Cancelled Customer',
      customer_status: 'cancelled'
    },
    subscription: {
      subscription_id: 'sub_cancelled',
      status: 'cancelled',
      cancellation_date: new Date().toISOString(),
      cancellation_reason: 'Customer request'
    },
    date: new Date().toISOString(),
    timestamp: Math.floor(Date.now() / 1000)
  },

  // Subscription paused
  subscriptionPaused: {
    event: 'order.subscription_paused',
    event_id: 'evt_pause_' + Date.now(),
    thrivecart_account: 'habittracker',
    customer: {
      customer_id: 'cust_pause',
      email: '<EMAIL>',
      name: 'Paused Customer'
    },
    subscription: {
      subscription_id: 'sub_paused',
      status: 'paused',
      pause_date: new Date().toISOString(),
      resume_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
    },
    date: new Date().toISOString(),
    timestamp: Math.floor(Date.now() / 1000)
  },

  // Subscription resumed
  subscriptionResumed: {
    event: 'order.subscription_resumed',
    event_id: 'evt_resume_' + Date.now(),
    thrivecart_account: 'habittracker',
    customer: {
      customer_id: 'cust_resume',
      email: '<EMAIL>',
      name: 'Resumed Customer'
    },
    subscription: {
      subscription_id: 'sub_resumed',
      status: 'active',
      resume_date: new Date().toISOString(),
      next_payment_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
    },
    date: new Date().toISOString(),
    timestamp: Math.floor(Date.now() / 1000)
  }
};

// Function to send webhook
async function sendWebhook(name, payload) {
  console.log(`\n📤 Sending ${name} webhook...`);
  console.log(`   Event: ${payload.event}`);
  console.log(`   Customer: ${payload.customer?.email}`);
  
  try {
    const response = await axios.post(
      `${BASE_URL}${WEBHOOK_ENDPOINT}`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          'X-TC-Event': payload.event,
          'X-TC-Account': 'habittracker',
          // Add signature if webhook secret is configured
          ...(process.env.THRIVECART_WEBHOOK_SECRET && {
            'X-TC-Hmac-SHA256': require('crypto')
              .createHmac('sha256', process.env.THRIVECART_WEBHOOK_SECRET)
              .update(JSON.stringify(payload))
              .digest('hex')
          })
        }
      }
    );
    
    console.log(`✅ Success: ${response.data ? JSON.stringify(response.data) : 'OK'}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed: ${error.response?.data ? JSON.stringify(error.response.data) : error.message}`);
    return false;
  }
}

// Function to test specific webhook
async function testWebhook(webhookType) {
  const payload = webhookPayloads[webhookType];
  if (!payload) {
    console.error(`Unknown webhook type: ${webhookType}`);
    console.log('Available types:', Object.keys(webhookPayloads).join(', '));
    return;
  }
  
  await sendWebhook(webhookType, payload);
}

// Function to test all webhooks
async function testAllWebhooks() {
  console.log('🚀 Testing All ThriveCart Webhooks');
  console.log('=' .repeat(50));
  console.log(`Target: ${BASE_URL}${WEBHOOK_ENDPOINT}`);
  console.log('=' .repeat(50));
  
  const results = {};
  
  for (const [name, payload] of Object.entries(webhookPayloads)) {
    results[name] = await sendWebhook(name, payload);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between webhooks
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('📊 Test Results Summary:');
  console.log('=' .repeat(50));
  
  let passed = 0;
  let failed = 0;
  
  for (const [name, result] of Object.entries(results)) {
    console.log(`${result ? '✅' : '❌'} ${name}`);
    if (result) passed++;
    else failed++;
  }
  
  console.log('\n' + '-' .repeat(50));
  console.log(`Total: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log('\n🎉 All webhooks processed successfully!');
  } else {
    console.log(`\n⚠️  ${failed} webhook(s) failed. Check logs for details.`);
  }
}

// Function to test custom webhook
async function testCustomWebhook(email, amount, productType = 'monthly') {
  const customPayload = {
    event: 'order.success',
    event_id: 'evt_custom_' + Date.now(),
    customer: {
      customer_id: 'cust_custom_' + Date.now(),
      email: email,
      name: 'Custom Test User'
    },
    order: {
      order_id: 'ord_custom_' + Date.now(),
      total: amount,
      currency: 'USD',
      status: 'success'
    },
    product: {
      product_name: productType === 'yearly' ? 'Habit Tracker Yearly' : 'Habit Tracker Monthly'
    },
    date: new Date().toISOString()
  };
  
  console.log(`\n🧪 Testing custom webhook for ${email} ($${amount} ${productType})`);
  await sendWebhook('custom', customPayload);
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // No arguments - test all webhooks
    testAllWebhooks();
  } else if (args[0] === '--help' || args[0] === '-h') {
    console.log(`
ThriveCart Webhook Testing Script

Usage:
  node test-thrivecart-webhooks.js                  # Test all webhooks
  node test-thrivecart-webhooks.js <type>          # Test specific webhook
  node test-thrivecart-webhooks.js custom <email> <amount> [monthly|yearly]
  
Available webhook types:
  - monthlyPurchase      # New monthly subscription
  - yearlyPurchase       # New yearly subscription  
  - subscriptionRenewal  # Recurring payment
  - paymentFailed        # Failed payment
  - refund              # Refund processed
  - subscriptionCancelled # Subscription cancelled
  - subscriptionPaused   # Subscription paused
  - subscriptionResumed  # Subscription resumed
  
Examples:
  node test-thrivecart-webhooks.js monthlyPurchase
  node test-thrivecart-webhooks.<NAME_EMAIL> 5 monthly
  node test-thrivecart-webhooks.<NAME_EMAIL> 30 yearly
    `);
  } else if (args[0] === 'custom') {
    const email = args[1] || '<EMAIL>';
    const amount = parseFloat(args[2]) || 5.00;
    const type = args[3] || 'monthly';
    testCustomWebhook(email, amount, type);
  } else {
    // Test specific webhook type
    testWebhook(args[0]);
  }
}

module.exports = {
  webhookPayloads,
  sendWebhook,
  testWebhook,
  testAllWebhooks,
  testCustomWebhook
};