const axios = require('axios');

async function testAgeVerification() {
  try {
    console.log('\n=== TESTING AGE VERIFICATION VIA API ===\n');
    
    // Test phone number
    const testPhone = '+15559999' + Math.floor(Math.random() * 1000);
    console.log(`Test phone: ${testPhone}`);
    
    // Test messages
    const testMessages = [
      { message: 'START HABIT-TEST123', description: 'Initial activation' },
      { message: '25', description: 'Valid age input' },
      { message: 'yes', description: 'Privacy consent' },
      { message: 'accept terms', description: 'Terms acceptance' }
    ];
    
    for (const test of testMessages) {
      console.log(`\nSending: "${test.message}" (${test.description})`);
      console.log('---');
      
      try {
        const response = await axios.post('http://localhost:3001/webhook', {
          From: `whatsapp:${testPhone}`,
          Body: test.message,
          ProfileName: 'Test User'
        }, {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
        });
        
        console.log('Response:', response.data);
      } catch (error) {
        console.log('Error:', error.response?.data || error.message);
      }
      
      // Small delay between messages
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('\n✅ Test sequence completed!\n');
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

testAgeVerification();