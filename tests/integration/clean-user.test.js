#!/usr/bin/env node

require('dotenv').config();
const stateMachine = require('./src/services/stateMachine');
const User = require('./src/models/User');
const pool = require('./src/db/connection');

async function testCleanUser() {
  console.log('Testing Clean User Experience\n');
  console.log('='.repeat(50));

  try {
    // Simulate a real WhatsApp user
    const testPhone = 'whatsapp:+16505551234';
    
    console.log(`New user messaging from: ${testPhone}\n`);
    
    // This simulates what happens in the webhook
    const user = await User.findOrCreate(testPhone);
    
    console.log('Created/Found User:');
    console.log(`  ID: ${user.id}`);
    console.log(`  Phone: ${user.phone}`);
    console.log(`  Display Name: ${user.display_name || '(not set)'}`);
    console.log(`  Status: ${user.status}`);
    console.log(`  Is Unlocked: ${user.is_unlocked}`);
    
    // Check habits
    const habitsResult = await pool.query(
      'SELECT COUNT(*) as count FROM habits WHERE user_id = $1',
      [user.id]
    );
    console.log(`  Habits: ${habitsResult.rows[0].count}`);
    
    // Send "menu" - first interaction
    console.log('\n' + '='.repeat(50));
    console.log('User sends: "menu"');
    console.log('='.repeat(50) + '\n');
    
    const response = await stateMachine.processMessage(user, 'menu');
    console.log(response.message);
    
    console.log('\n' + '='.repeat(50));
    console.log('✅ User isolation verified:');
    console.log('  - New user created with LOCKED status');
    console.log('  - No pre-loaded habits');
    console.log('  - Clean slate for onboarding');
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await pool.end();
  }
}

testCleanUser();