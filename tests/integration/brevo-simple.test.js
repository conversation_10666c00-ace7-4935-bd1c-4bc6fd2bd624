#!/usr/bin/env node

require('dotenv').config();
const { createTransport } = require('nodemailer');

console.log('Testing Brevo SMTP email with simple configuration...\n');

// Create transporter without verification
const transporter = createTransport({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || '2525'),
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  },
  tls: {
    rejectUnauthorized: false // Allow self-signed certificates
  },
  connectionTimeout: 10000, // 10 seconds
  greetingTimeout: 10000,   // 10 seconds
  socketTimeout: 10000      // 10 seconds
});

async function sendTestEmail() {
  try {
    console.log('Attempting to send test email...');
    
    const info = await transporter.sendMail({
      from: `"Lockin Habit Tracker" <${process.env.EMAIL_FROM}>`,
      to: '<EMAIL>',
      subject: 'Your Lockin Habit Tracker Access Code',
      text: 'Test email from Lockin Habit Tracker\n\nAccess Code: HABIT-TEST123',
      html: '<h1>Test Email</h1><p>Access Code: <strong>HABIT-TEST123</strong></p>'
    });

    console.log('✅ Email sent successfully!');
    console.log('Message ID:', info.messageId);
    console.log('Response:', info.response);
    
  } catch (error) {
    console.error('❌ Error sending email:', error.message);
    console.error('Error code:', error.code);
    console.error('Error command:', error.command);
  }
}

sendTestEmail();