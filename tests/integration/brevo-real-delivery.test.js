#!/usr/bin/env node

require('dotenv').config();
const { createTransport } = require('nodemailer');

async function testBrevoRealDelivery() {
  console.log('🔍 BREVO REAL EMAIL DELIVERY TEST');
  console.log('=' .repeat(40));
  
  console.log('\n1️⃣ Configuration Check:');
  console.log(`   SMTP Host: ${process.env.SMTP_HOST}`);
  console.log(`   SMTP Port: ${process.env.SMTP_PORT}`);
  console.log(`   SMTP User: ${process.env.SMTP_USER}`);
  console.log(`   SMTP Pass: ${process.env.SMTP_PASS ? 'SET' : 'NOT SET'}`);
  console.log(`   From Email: ${process.env.EMAIL_FROM}`);
  
  // Create transporter with detailed logging
  const transporter = createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT),
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    },
    debug: true, // Enable debug output
    logger: true // Log to console
  });

  console.log('\n2️⃣ Testing SMTP Connection...');
  
  try {
    // Verify connection
    console.log('   Verifying SMTP connection...');
    await transporter.verify();
    console.log('   ✅ SMTP connection verified');
  } catch (error) {
    console.log('   ❌ SMTP verification failed:', error.message);
    console.log('   Full error:', error);
    return;
  }

  console.log('\n3️⃣ Sending Test Email...');
  
  const testEmail = '<EMAIL>'; // Public test mailbox
  const testSubject = `Brevo Test - ${new Date().toISOString()}`;
  
  try {
    console.log(`   Sending to: ${testEmail}`);
    console.log(`   Subject: ${testSubject}`);
    
    const info = await transporter.sendMail({
      from: `"Lockin Habit Tracker Test" <${process.env.EMAIL_FROM}>`,
      to: testEmail,
      subject: testSubject,
      html: `
        <h1>Brevo SMTP Test</h1>
        <p>This is a test email sent at: ${new Date().toISOString()}</p>
        <p>If you receive this, Brevo SMTP is working correctly!</p>
        <hr>
        <p>Test ID: ${Date.now()}</p>
      `,
      text: `
Brevo SMTP Test

This is a test email sent at: ${new Date().toISOString()}
If you receive this, Brevo SMTP is working correctly!

Test ID: ${Date.now()}
      `
    });

    console.log('   ✅ Email sent successfully!');
    console.log(`   Message ID: ${info.messageId}`);
    console.log(`   Response: ${info.response}`);
    console.log(`   Accepted: ${info.accepted?.join(', ') || 'N/A'}`);
    console.log(`   Rejected: ${info.rejected?.join(', ') || 'None'}`);
    
    if (info.envelope) {
      console.log(`   Envelope From: ${info.envelope.from}`);
      console.log(`   Envelope To: ${info.envelope.to?.join(', ') || 'N/A'}`);
    }
    
    console.log('\n4️⃣ Delivery Status:');
    console.log(`   📧 Check your inbox at: ${testEmail}`);
    console.log('   🌐 Or visit: https://www.mailinator.com/v4/public/inboxes.jsp?to=' + testEmail);
    console.log(`   🔍 Search for subject: "${testSubject}"`);
    
  } catch (error) {
    console.log('   ❌ Email sending failed!');
    console.log(`   Error: ${error.message}`);
    console.log(`   Code: ${error.code || 'N/A'}`);
    console.log(`   Command: ${error.command || 'N/A'}`);
    
    if (error.response) {
      console.log(`   SMTP Response: ${error.response}`);
    }
    
    console.log('\n   Full error details:');
    console.log(error);
  }
  
  console.log('\n' + '=' .repeat(40));
}

testBrevoRealDelivery().catch(console.error);