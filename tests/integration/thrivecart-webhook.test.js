#!/usr/bin/env node

/**
 * Test script for ThriveCart webhook integration
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

// Test webhook payloads
const testPayloads = {
  // New order/purchase
  newOrder: {
    event: 'order.success',
    customer: {
      customer_id: 'CUST-123456',
      email: '<EMAIL>',
      name: 'Test Customer'
    },
    order: {
      order_id: 'ORD-789012',
      total: 5.00,
      currency: 'USD'
    },
    product: {
      product_name: 'Habit Tracker Monthly'
    }
  },
  
  // Yearly subscription
  yearlyOrder: {
    event: 'order.success',
    customer: {
      customer_id: 'CUST-YEARLY-123',
      email: '<EMAIL>',
      name: 'Yearly Customer'
    },
    order: {
      order_id: 'ORD-YEARLY-456',
      total: 30.00,
      currency: 'USD'
    },
    product: {
      product_name: 'Habit Tracker Yearly'
    }
  },
  
  // Subscription renewal
  renewal: {
    event: 'order.rebill_success',
    customer: {
      customer_id: 'CUST-123456',
      email: '<EMAIL>',
      name: 'Test Customer'
    },
    order: {
      order_id: 'ORD-RENEWAL-345',
      total: 5.00,
      currency: 'USD'
    }
  },
  
  // Refund
  refund: {
    event: 'order.refund',
    customer: {
      customer_id: 'CUST-123456',
      email: '<EMAIL>',
      name: 'Test Customer'
    },
    order: {
      order_id: 'ORD-REFUND-678',
      total: 5.00,
      currency: 'USD'
    }
  },
  
  // Cancellation
  cancellation: {
    event: 'order.subscription_cancelled',
    customer: {
      customer_id: 'CUST-123456',
      email: '<EMAIL>',
      name: 'Test Customer'
    }
  }
};

async function testWebhook(eventType, payload) {
  try {
    console.log(`\n📤 Testing ${eventType}...`);
    
    const response = await axios.post(`${BASE_URL}/webhook/thrivecart`, payload, {
      headers: {
        'Content-Type': 'application/json',
        // Add signature header if needed for testing
        // 'X-TC-Hmac-SHA256': 'test-signature'
      }
    });
    
    console.log(`✅ ${eventType} successful:`, response.data);
    return response.data;
  } catch (error) {
    console.error(`❌ ${eventType} failed:`, error.response?.data || error.message);
    return null;
  }
}

async function checkStatus() {
  try {
    console.log('\n📊 Checking webhook status...');
    const response = await axios.get(`${BASE_URL}/test/status`);
    console.log('Status:', response.data);
  } catch (error) {
    console.error('Status check failed:', error.message);
  }
}

async function testSimpleWebhook(email, subscriptionType = 'monthly') {
  try {
    console.log(`\n🧪 Testing simple webhook for ${email} (${subscriptionType})...`);
    
    const response = await axios.post(`${BASE_URL}/test/webhook/thrivecart`, {
      email,
      subscriptionType
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Test webhook successful:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Test webhook failed:', error.response?.data || error.message);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting ThriveCart Webhook Tests');
  console.log('=' .repeat(50));
  
  // Check if test mode is enabled
  if (process.env.PAYMENT_TEST_MODE !== 'true') {
    console.log('⚠️  Warning: PAYMENT_TEST_MODE is not set to true');
    console.log('Set PAYMENT_TEST_MODE=true in your .env file to enable test endpoints');
  }
  
  // Test simple webhook endpoint (if available)
  await testSimpleWebhook('<EMAIL>', 'monthly');
  await testSimpleWebhook('<EMAIL>', 'yearly');
  
  // Test full webhook payloads
  await testWebhook('New Monthly Order', testPayloads.newOrder);
  await testWebhook('New Yearly Order', testPayloads.yearlyOrder);
  await testWebhook('Subscription Renewal', testPayloads.renewal);
  await testWebhook('Refund', testPayloads.refund);
  await testWebhook('Cancellation', testPayloads.cancellation);
  
  // Check final status
  await checkStatus();
  
  console.log('\n✨ Tests complete!');
}

// Run tests if this is the main module
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testWebhook, testPayloads };