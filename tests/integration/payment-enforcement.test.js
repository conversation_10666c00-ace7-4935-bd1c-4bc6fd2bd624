#!/usr/bin/env node

/**
 * Payment Enforcement Test Script
 * Tests the complete payment verification workflow
 */

const axios = require('axios');
const { Pool } = require('pg');

const BASE_URL = 'http://localhost:3001';
const TEST_PHONE = '+15551234567';
const TEST_EMAIL = '<EMAIL>';

// Database connection
const pool = new Pool({
  user: 'postgres',
  password: 'postgres',
  host: 'localhost',
  database: 'lockin',
  port: 5432
});

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function simulateWhatsAppMessage(body, from = TEST_PHONE, to = '+19035155547') {
  try {
    // Create or find user first
    await pool.query(
      `INSERT INTO users (phone, status, created_at, updated_at, last_active) 
       VALUES ($1, 'LOCKED', NOW(), NOW(), NOW()) 
       ON CONFLICT (phone) DO NOTHING`,
      [from.replace('whatsapp:', '')]
    );

    const response = await axios.post(`${BASE_URL}/webhook/whatsapp`, 
      `Body=${encodeURIComponent(body)}&From=whatsapp:${from}&To=whatsapp:${to}`,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        timeout: 5000
      }
    );
    
    // Extract message from TwiML response
    const messageMatch = response.data.match(/<Message>(.*?)<\/Message>/s);
    return messageMatch ? messageMatch[1].trim() : 'No message found';
  } catch (error) {
    if (error.response) {
      return `Error: ${error.response.status} - ${error.response.data}`;
    }
    return `Network Error: ${error.message}`;
  }
}

async function testPaymentEnforcement() {
  console.log('🚀 Starting Payment Enforcement Test\n');
  
  try {
    // Clear existing test data
    console.log('🧹 Clearing test data...');
    await axios.delete(`${BASE_URL}/test/clear-data`);
    
    // Reset any existing user
    await pool.query(
      `UPDATE users 
       SET status = 'LOCKED', current_state = 'MAIN_MENU', session_context = '{}', display_name = NULL 
       WHERE phone = $1`,
      [TEST_PHONE]
    );
    
    console.log('✅ Test environment cleaned\n');

    // ============================================================
    // STEP 1: Test paywall enforcement on various commands
    // ============================================================
    console.log('🔒 STEP 1: Testing paywall enforcement');
    console.log('==========================================\n');
    
    const testCommands = ['hello', 'menu', '1', 'log', 'stats', 'help', 'start', 'settings'];
    
    for (const command of testCommands) {
      console.log(`Testing command: "${command}"`);
      const response = await simulateWhatsAppMessage(command, TEST_PHONE);
      
      if (response.includes('ACCESS REQUIRED') || response.includes('🚫')) {
        console.log('✅ Paywall correctly shown\n');
      } else if (response.includes('Error')) {
        console.log(`❌ Error: ${response}\n`);
      } else {
        console.log(`⚠️  Unexpected response: ${response.substring(0, 100)}...\n`);
      }
      
      await delay(500); // Small delay between tests
    }

    // ============================================================
    // STEP 2: Create payment and test access code
    // ============================================================
    console.log('💳 STEP 2: Creating test payment');
    console.log('=================================\n');
    
    const paymentResponse = await axios.post(`${BASE_URL}/test/create-payment`, {
      email: TEST_EMAIL,
      subscriptionType: 'monthly'
    });
    
    console.log('✅ Payment created');
    await delay(1000);
    
    // Get access code
    const paidUserResult = await pool.query(
      'SELECT access_code FROM paid_users WHERE email = $1 ORDER BY created_at DESC LIMIT 1',
      [TEST_EMAIL]
    );
    
    if (paidUserResult.rows.length === 0) {
      throw new Error('No paid user created');
    }
    
    const accessCode = paidUserResult.rows[0].access_code;
    console.log(`📧 Access code generated: ${accessCode}\n`);

    // ============================================================
    // STEP 3: Test access code activation
    // ============================================================
    console.log('🔑 STEP 3: Testing access code activation');
    console.log('==========================================\n');
    
    // Test invalid format first
    console.log('Testing invalid access code format...');
    const invalidResponse = await simulateWhatsAppMessage('START INVALID', TEST_PHONE);
    if (invalidResponse.includes('Invalid format')) {
      console.log('✅ Invalid format correctly rejected\n');
    } else {
      console.log(`⚠️  Unexpected response: ${invalidResponse.substring(0, 100)}...\n`);
    }
    
    // Test valid access code
    console.log('Testing valid access code activation...');
    const activationResponse = await simulateWhatsAppMessage(`START ${accessCode}`, TEST_PHONE);
    if (activationResponse.includes('Success') || activationResponse.includes('🎉')) {
      console.log('✅ Access code successfully activated\n');
    } else {
      console.log(`❌ Activation failed: ${activationResponse.substring(0, 200)}...\n`);
    }

    // ============================================================
    // STEP 4: Test bot access after payment
    // ============================================================
    console.log('🎯 STEP 4: Testing bot access after payment');
    console.log('============================================\n');
    
    // Test that user can now access bot functions
    const menuResponse = await simulateWhatsAppMessage('menu', TEST_PHONE);
    if (menuResponse.includes('What should I call you') || menuResponse.includes('habit')) {
      console.log('✅ Bot functions now accessible after payment\n');
    } else {
      console.log(`⚠️  Unexpected menu response: ${menuResponse.substring(0, 200)}...\n`);
    }

    // ============================================================
    // STEP 5: Test RESET_PAYMENT_TEST command
    // ============================================================
    console.log('🔄 STEP 5: Testing payment test reset');
    console.log('=====================================\n');
    
    const resetResponse = await simulateWhatsAppMessage('RESET_PAYMENT_TEST', TEST_PHONE);
    if (resetResponse.includes('PAYMENT TEST RESET COMPLETE')) {
      console.log('✅ Payment test reset successful\n');
    } else {
      console.log(`❌ Reset failed: ${resetResponse.substring(0, 200)}...\n`);
    }

    // ============================================================
    // STEP 6: Verify paywall is back after reset
    // ============================================================
    console.log('🔒 STEP 6: Verifying paywall after reset');
    console.log('=========================================\n');
    
    const postResetResponse = await simulateWhatsAppMessage('hello', TEST_PHONE);
    if (postResetResponse.includes('ACCESS REQUIRED') || postResetResponse.includes('🚫')) {
      console.log('✅ Paywall correctly restored after reset\n');
    } else {
      console.log(`❌ Paywall not shown: ${postResetResponse.substring(0, 200)}...\n`);
    }

    // ============================================================
    // SUMMARY
    // ============================================================
    console.log('📊 FINAL SUMMARY');
    console.log('================\n');
    
    const finalStats = await axios.get(`${BASE_URL}/test/status`);
    console.log('Database stats:', finalStats.data.stats);
    
    // Check user status
    const userStatus = await pool.query(
      'SELECT phone, status, current_state, display_name FROM users WHERE phone = $1',
      [TEST_PHONE]
    );
    
    if (userStatus.rows.length > 0) {
      console.log('User status:', userStatus.rows[0]);
    }
    
    console.log('\n✨ Payment enforcement test completed!');
    console.log('\n📋 VERIFICATION CHECKLIST:');
    console.log('- ✅ Paywall blocks all commands before payment');
    console.log('- ✅ START HABIT-XXXXX activates access');
    console.log('- ✅ Bot functions work after payment');
    console.log('- ✅ RESET_PAYMENT_TEST clears payment status');
    console.log('- ✅ Paywall returns after reset');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  } finally {
    await pool.end();
  }
}

// Run test
testPaymentEnforcement().catch(console.error);