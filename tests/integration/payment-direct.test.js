#!/usr/bin/env node

/**
 * Direct Payment Enforcement Test
 * Tests payment verification by directly calling the state machine
 */

const { Pool } = require('pg');
const stateMachine = require('./src/services/stateMachinePaymentEnforced');
const User = require('./src/models/User');
const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const TEST_PHONE = '+15551234567';
const TEST_EMAIL = '<EMAIL>';

// Database connection
const pool = new Pool({
  user: 'postgres',
  password: 'postgres',
  host: 'localhost',
  database: 'lockin',
  port: 5432
});

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testDirectPaymentEnforcement() {
  console.log('🚀 Starting Direct Payment Enforcement Test\n');
  
  try {
    // Clear existing test data
    console.log('🧹 Clearing test data...');
    await axios.delete(`${BASE_URL}/test/clear-data`);
    
    // Create/reset user
    await pool.query(
      `INSERT INTO users (phone, status, current_state, created_at, updated_at, last_active) 
       VALUES ($1, 'LOCKED', 'MAIN_MENU', NOW(), NOW(), NOW()) 
       ON CONFLICT (phone) DO UPDATE SET 
       status = 'LOCKED', current_state = 'MAIN_MENU', session_context = '{}', display_name = NULL`,
      [TEST_PHONE]
    );
    
    const userResult = await pool.query('SELECT * FROM users WHERE phone = $1', [TEST_PHONE]);
    const user = userResult.rows[0];
    
    console.log('✅ Test user created:', {
      id: user.id,
      phone: user.phone,
      status: user.status
    });
    console.log();

    // ============================================================
    // STEP 1: Test paywall enforcement on various commands
    // ============================================================
    console.log('🔒 STEP 1: Testing paywall enforcement');
    console.log('==========================================\n');
    
    const testCommands = ['hello', 'menu', '1', 'log', 'stats', 'help', 'settings'];
    
    for (const command of testCommands) {
      console.log(`Testing command: "${command}"`);
      try {
        const response = await stateMachine.processMessage(user, command);
        
        if (response.message.includes('ACCESS REQUIRED') || response.message.includes('🚫')) {
          console.log('✅ Paywall correctly shown');
        } else {
          console.log(`⚠️  Unexpected response: ${response.message.substring(0, 100)}...`);
        }
      } catch (error) {
        console.log(`❌ Error: ${error.message}`);
      }
      console.log();
    }

    // ============================================================
    // STEP 2: Create payment and get access code
    // ============================================================
    console.log('💳 STEP 2: Creating test payment');
    console.log('=================================\n');
    
    await axios.post(`${BASE_URL}/test/create-payment`, {
      email: TEST_EMAIL,
      subscriptionType: 'monthly'
    });
    
    console.log('✅ Payment created');
    await delay(1000);
    
    const paidUserResult = await pool.query(
      'SELECT access_code FROM paid_users WHERE email = $1 ORDER BY created_at DESC LIMIT 1',
      [TEST_EMAIL]
    );
    
    const accessCode = paidUserResult.rows[0].access_code;
    console.log(`📧 Access code: ${accessCode}\n`);

    // ============================================================
    // STEP 3: Test access code activation
    // ============================================================
    console.log('🔑 STEP 3: Testing access code activation');
    console.log('==========================================\n');
    
    // Test invalid format
    console.log('Testing invalid format...');
    try {
      const invalidResponse = await stateMachine.processMessage(user, 'START INVALID');
      if (invalidResponse.message.includes('Invalid format')) {
        console.log('✅ Invalid format rejected');
      } else {
        console.log(`⚠️  Unexpected: ${invalidResponse.message.substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
    console.log();
    
    // Test valid access code
    console.log('Testing valid access code...');
    try {
      const activationResponse = await stateMachine.processMessage(user, `START ${accessCode}`);
      if (activationResponse.message.includes('Success') || activationResponse.message.includes('🎉')) {
        console.log('✅ Access code activated successfully');
        
        // Update user object with new status
        const updatedUserResult = await pool.query('SELECT * FROM users WHERE phone = $1', [TEST_PHONE]);
        user.status = updatedUserResult.rows[0].status;
        user.current_state = updatedUserResult.rows[0].current_state;
      } else {
        console.log(`❌ Activation failed: ${activationResponse.message.substring(0, 200)}...`);
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
    console.log();

    // ============================================================
    // STEP 4: Test bot access after payment
    // ============================================================
    console.log('🎯 STEP 4: Testing bot access after payment');
    console.log('============================================\n');
    
    // Get updated user status
    const updatedUserResult = await pool.query('SELECT * FROM users WHERE phone = $1', [TEST_PHONE]);
    const updatedUser = updatedUserResult.rows[0];
    
    console.log('Updated user status:', {
      status: updatedUser.status,
      state: updatedUser.current_state
    });
    
    try {
      const menuResponse = await stateMachine.processMessage(updatedUser, 'hello');
      if (menuResponse.message.includes('What should I call you') || 
          menuResponse.message.includes('Welcome') ||
          !menuResponse.message.includes('ACCESS REQUIRED')) {
        console.log('✅ Bot functions accessible after payment');
      } else {
        console.log(`⚠️  Still showing paywall: ${menuResponse.message.substring(0, 200)}...`);
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
    console.log();

    // ============================================================
    // STEP 5: Test RESET_PAYMENT_TEST command
    // ============================================================
    console.log('🔄 STEP 5: Testing payment reset');
    console.log('=================================\n');
    
    try {
      const resetResponse = await stateMachine.processMessage(updatedUser, 'RESET_PAYMENT_TEST');
      if (resetResponse.message.includes('PAYMENT TEST RESET COMPLETE')) {
        console.log('✅ Payment reset successful');
        
        // Get updated user
        const resetUserResult = await pool.query('SELECT * FROM users WHERE phone = $1', [TEST_PHONE]);
        user.status = resetUserResult.rows[0].status;
        user.current_state = resetUserResult.rows[0].current_state;
      } else {
        console.log(`❌ Reset failed: ${resetResponse.message.substring(0, 200)}...`);
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
    console.log();

    // ============================================================
    // STEP 6: Verify paywall after reset
    // ============================================================
    console.log('🔒 STEP 6: Verifying paywall after reset');
    console.log('=========================================\n');
    
    try {
      const postResetResponse = await stateMachine.processMessage(user, 'hello');
      if (postResetResponse.message.includes('ACCESS REQUIRED') || postResetResponse.message.includes('🚫')) {
        console.log('✅ Paywall correctly restored after reset');
      } else {
        console.log(`❌ Paywall not shown: ${postResetResponse.message.substring(0, 200)}...`);
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
    console.log();

    // ============================================================
    // SUMMARY
    // ============================================================
    console.log('📊 FINAL SUMMARY');
    console.log('================\n');
    
    const finalUserResult = await pool.query('SELECT * FROM users WHERE phone = $1', [TEST_PHONE]);
    console.log('Final user status:', {
      status: finalUserResult.rows[0].status,
      state: finalUserResult.rows[0].current_state,
      display_name: finalUserResult.rows[0].display_name
    });
    
    const finalStats = await axios.get(`${BASE_URL}/test/status`);
    console.log('Database stats:', finalStats.data.stats);
    
    console.log('\n✨ Direct payment enforcement test completed!');
    console.log('\n📋 VERIFICATION RESULTS:');
    console.log('- 🔒 Paywall blocks commands before payment');
    console.log('- 🔑 START HABIT-XXXXX activates access');
    console.log('- 🎯 Bot accessible after payment');
    console.log('- 🔄 RESET_PAYMENT_TEST clears status');
    console.log('- 🚫 Paywall restored after reset');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    await pool.end();
  }
}

// Run test
testDirectPaymentEnforcement().catch(console.error);