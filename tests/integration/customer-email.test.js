#!/usr/bin/env node

require('dotenv').config();
const nodemailer = require('nodemailer');
const emailService = require('./src/services/emailService');

async function sendTestToCustmerEmail() {
  try {
    console.log('📧 SENDING TEST EMAIL TO YOUR ADDRESS...');
    
    // Use same data as a real customer purchase
    const templateData = {
      'botPhone': '+19035155547',
      'accessCode': 'DEBUG-TEST',
      'subscriptionType': 'yearly',
      'subscription_type': 'Annual', 
      'subscription_price': '$39.99/year',
      'primary_access_code': 'DEBUG-TEST',
      'isAffiliate': true,
      'affiliateCode': 'DEBUG-AFF'
    };
    
    const { subject, html, text } = emailService.getWelcomeDynamicTemplate(templateData);
    
    console.log('📊 Email stats:');
    console.log(`   Subject: ${subject}`);
    console.log(`   HTML length: ${html.length} characters`);
    
    const step5Index = html.indexOf('Start building unstoppable habits!');
    const afterStep5 = html.substring(step5Index + 'Start building unstoppable habits!'.length);
    console.log(`   Characters after step 5: ${afterStep5.length}`);
    
    // Check for the CSS issues you mentioned
    const brokenCSS = [];
    if (html.includes('align-items: cent') && !html.includes('align-items: center')) {
      brokenCSS.push('align-items truncated');
    }
    
    if (brokenCSS.length > 0) {
      console.log('❌ BROKEN CSS DETECTED:');
      brokenCSS.forEach(issue => console.log(`   ${issue}`));
    } else {
      console.log('✅ No broken CSS detected');
    }
    
    // Create transporter
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_PORT === '465',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
    
    console.log('\n📧 <NAME_EMAIL>...');
    
    const result = await transporter.sendMail({
      from: `"DEBUG Lock In" <${process.env.EMAIL_FROM}>`,
      to: '<EMAIL>',
      subject: 'DEBUG - CSS Fix Test Email',
      html: html,
      text: text
    });
    
    console.log('✅ Test email sent!');
    console.log('Message ID:', result.messageId);
    
    console.log('\n🎯 CHECK THIS EMAIL:');
    console.log('   - Does it show complete content after step 5?');
    console.log('   - Can you see Rich signature, affiliate section, social links?');
    console.log('   - Is there any truncation or broken HTML?');
    
    // Save for inspection
    require('fs').writeFileSync('/var/www/lockin/debug-test-email.html', html);
    console.log('\n💾 Email saved to: debug-test-email.html');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

sendTestToCustmerEmail().catch(console.error);