#!/usr/bin/env node

require('dotenv').config();
const webhookController = require('./src/controllers/webhookController');
const pool = require('./src/db/connection');

async function testAutoReset() {
  try {
    const testPhone = '+27646921984';
    
    console.log('🧪 TESTING AUTO-RESET MECHANISM');
    console.log('='.repeat(60));
    
    // Step 1: Add some logs to the database manually
    console.log('\n📊 STEP 1: Adding test logs to database...');
    const client = await pool.connect();
    const user = await client.query('SELECT id FROM users WHERE phone = $1', [testPhone]);
    const userId = user.rows[0].id;
    
    // Insert test logs
    await client.query(`
      INSERT INTO habit_logs (user_id, habit_id, log_date, completed)
      SELECT $1, id, CURRENT_DATE, true
      FROM habits 
      WHERE user_id = $1 AND habit_number IN (1, 3, 5)
    `, [userId]);
    
    // Check logs before webhook
    const beforeCount = await client.query('SELECT COUNT(*) FROM habit_logs WHERE user_id = $1', [userId]);
    console.log(`   Added ${beforeCount.rows[0].count} test logs`);
    client.release();
    
    // Step 2: Call webhook (should trigger auto-reset)
    console.log('\n📱 STEP 2: Calling webhook (should auto-reset)...');
    
    const req = {
      body: {
        From: `whatsapp:${testPhone}`,
        Body: 'menu'
      }
    };
    
    let responseMessage = '';
    const res = {
      type: () => res,
      send: (data) => {
        const match = data.match(/<Message>(.*?)<\/Message>/s);
        responseMessage = match ? match[1] : data;
      }
    };
    
    await webhookController.handleIncomingMessage(req, res);
    
    // Step 3: Check if logs were cleared
    console.log('\n🔍 STEP 3: Checking if auto-reset worked...');
    const client2 = await pool.connect();
    const afterCount = await client2.query('SELECT COUNT(*) FROM habit_logs WHERE user_id = $1', [userId]);
    console.log(`   Logs remaining: ${afterCount.rows[0].count}`);
    client2.release();
    
    // Step 4: Show the menu response
    console.log('\n📋 STEP 4: Menu response:');
    console.log('─'.repeat(40));
    console.log(responseMessage);
    console.log('─'.repeat(40));
    
    // Step 5: Verify all habits show ⚠️
    console.log('\n✅ VERIFICATION:');
    if (afterCount.rows[0].count === '0') {
      console.log('   ✅ Auto-reset worked: All logs cleared');
    } else {
      console.log('   ❌ Auto-reset failed: Logs still exist');
    }
    
    if (responseMessage.includes('⚠️1. Subliminals')) {
      console.log('   ✅ Menu shows clean slate: All ⚠️ icons');
    } else {
      console.log('   ❌ Menu still shows logged habits');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await pool.end();
  }
}

testAutoReset().catch(console.error);