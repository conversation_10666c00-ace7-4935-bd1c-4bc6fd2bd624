#!/usr/bin/env node

require('dotenv').config();

async function testSimpleTemplate() {
  try {
    console.log('🧪 TESTING SIMPLE EMAIL TEMPLATE...');
    
    const emailService = require('./src/services/emailService');
    
    // Test data like actual customer
    const testData = {
      subscription_type: 'Annual',
      subscription_price: '$39.99/year',
      primary_access_code: 'HABIT-SIMPLE123',
      botPhone: '+19035155547',
      subscriptionTypeDisplay: 'Annual',
      pricingText: '$39.99/year',
      accessCode: 'HABIT-SIMPLE123',
      primaryCode: 'HABIT-SIMPLE123'
    };
    
    console.log('📊 TEST DATA:', JSON.stringify(testData, null, 2));
    
    // Generate template
    const { subject, html, text } = emailService.getWelcomeDynamicTemplate(testData);
    
    console.log('\n📧 GENERATED EMAIL:');
    console.log('Subject:', subject);
    console.log('HTML length:', html.length);
    console.log('Text length:', text.length);
    
    // Check for all footer content
    const footerChecks = [
      { name: 'Rich signature', check: html.includes("Let's Lock In") && html.includes('Rich') },
      { name: 'Social links', check: html.includes('@richvieren') },
      { name: 'Affiliate section', check: html.includes('25% recurring commission') },
      { name: 'Copyright', check: html.includes('© 2025 Lock In') },
      { name: 'Access code', check: html.includes('HABIT-SIMPLE123') },
      { name: 'Phone number', check: html.includes('+19035155547') }
    ];
    
    console.log('\n🔍 CONTENT CHECK:');
    footerChecks.forEach(({ name, check }) => {
      console.log(`  ${check ? '✅' : '❌'} ${name}`);
    });
    
    const allGood = footerChecks.every(({ check }) => check);
    console.log(`\n${allGood ? '🎉 SIMPLE TEMPLATE IS COMPLETE!' : '❌ SIMPLE TEMPLATE IS BROKEN!'}`);
    
    // Save for inspection
    require('fs').writeFileSync('/var/www/lockin/simple-template-test.html', html);
    console.log('\n💾 Saved to: simple-template-test.html');
    
    console.log('\n📏 COMPARISON WITH OLD TEMPLATE:');
    console.log('New simple template: ~', html.length, 'characters');
    console.log('Old complex template: ~9240 characters');
    console.log('Reduction:', ((9240 - html.length) / 9240 * 100).toFixed(1) + '%');
    
    console.log('\n✅ Simple template ready for deployment!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

testSimpleTemplate().catch(console.error);