#!/usr/bin/env node

require('dotenv').config();
const emailService = require('./src/services/emailService');

async function testContextIssue() {
  try {
    console.log('🔍 TESTING TEMPLATE CONTEXT ISSUE...');
    
    const testData = {
      'botPhone': '+19035155547',
      'accessCode': 'CONTEXT-TEST',
      'subscriptionType': 'yearly',
      'subscription_type': 'Annual', 
      'subscription_price': '$39.99/year',
      'primary_access_code': 'CONTEXT-TEST',
      'isAffiliate': true,
      'affiliateCode': 'CONTEXT-AFF'
    };
    
    const templateFunc = emailService.templates['welcome_dynamic'];
    
    console.log('\n1️⃣ TESTING DIRECT CALL (like queue does):');
    const result1 = templateFunc(testData);
    console.log('HTML length:', result1.html.length);
    const step5Index1 = result1.html.indexOf('Start building unstoppable habits!');
    const afterStep51 = result1.html.substring(step5Index1 + 'Start building unstoppable habits!'.length);
    console.log('Characters after step 5:', afterStep51.length);
    
    // Check for broken links
    const brokenLinks1 = result1.html.match(/<a[^>]*thrivecart[^>]*(?![>])/g);
    console.log('Broken links:', brokenLinks1 ? brokenLinks1.length : 0);
    
    console.log('\n2️⃣ TESTING WITH CONTEXT (like direct emails):');
    const result2 = templateFunc.call(emailService, testData);
    console.log('HTML length:', result2.html.length);
    const step5Index2 = result2.html.indexOf('Start building unstoppable habits!');
    const afterStep52 = result2.html.substring(step5Index2 + 'Start building unstoppable habits!'.length);
    console.log('Characters after step 5:', afterStep52.length);
    
    // Check for broken links
    const brokenLinks2 = result2.html.match(/<a[^>]*thrivecart[^>]*(?![>])/g);
    console.log('Broken links:', brokenLinks2 ? brokenLinks2.length : 0);
    
    console.log('\n📊 COMPARISON:');
    console.log('Same HTML length?', result1.html.length === result2.html.length);
    console.log('Same content?', result1.html === result2.html);
    
    if (result1.html !== result2.html) {
      console.log('🚨 DIFFERENT CONTENT - CONTEXT ISSUE CONFIRMED!');
      
      // Save both for comparison
      require('fs').writeFileSync('/var/www/lockin/direct-call.html', result1.html);
      require('fs').writeFileSync('/var/www/lockin/context-call.html', result2.html);
      console.log('💾 Saved both versions for comparison');
    } else {
      console.log('✅ Same content - not a context issue');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testContextIssue().catch(console.error);