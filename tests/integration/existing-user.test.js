#!/usr/bin/env node

require('dotenv').config();
const stateMachine = require('./src/services/stateMachine');
const pool = require('./src/db/connection');

async function testExistingUser() {
  console.log('Testing Existing User With Habits\n');
  console.log('='.repeat(50));

  try {
    // Get user ID 1 which has habits
    const userResult = await pool.query(
      'SELECT * FROM users WHERE id = 1'
    );
    const user = userResult.rows[0];
    
    console.log('User Details:');
    console.log(`  ID: ${user.id}`);
    console.log(`  Phone: ${user.phone}`);
    console.log(`  Name: ${user.display_name}`);
    console.log(`  Status: ${user.status}`);
    
    // Get habits
    const habitsResult = await pool.query(
      'SELECT habit_number, habit_name FROM habits WHERE user_id = $1 ORDER BY habit_number',
      [user.id]
    );
    
    console.log('\nUser Habits:');
    habitsResult.rows.forEach(h => {
      console.log(`  ${h.habit_number}. ${h.habit_name}`);
    });
    
    // Send "menu"
    console.log('\n' + '='.repeat(50));
    console.log('Sending "menu" from this user:');
    console.log('='.repeat(50) + '\n');
    
    const response = await stateMachine.processMessage(user, 'menu');
    console.log(response.message);
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await pool.end();
  }
}

testExistingUser();