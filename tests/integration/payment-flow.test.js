#!/usr/bin/env node

/**
 * FastSpring Payment Flow Test Script
 * Tests the complete payment-to-bot-access pipeline
 */

const axios = require('axios');
const { Pool } = require('pg');

const BASE_URL = 'http://localhost:3001';
const TEST_EMAIL = '<EMAIL>';
const TEST_PHONE = '+1234567890';

// Database connection
const pool = new Pool({
  user: 'postgres',
  password: 'postgres',
  host: 'localhost',
  database: 'lockin',
  port: 5432
});

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testPaymentFlow() {
  console.log('🚀 Starting FastSpring Payment Flow Test\n');
  
  try {
    // Step 1: Check test mode status
    console.log('1️⃣ Checking test mode status...');
    const statusResponse = await axios.get(`${BASE_URL}/test/status`);
    console.log('✅ Test mode active:', statusResponse.data.testMode);
    console.log('   Initial stats:', statusResponse.data.stats);
    
    // Step 2: Clear any existing test data
    console.log('\n2️⃣ Clearing existing test data...');
    await axios.delete(`${BASE_URL}/test/clear-data`);
    console.log('✅ Test data cleared');
    
    // Step 3: Create a monthly subscription payment
    console.log('\n3️⃣ Creating monthly subscription payment...');
    const monthlyPayment = await axios.post(`${BASE_URL}/test/create-payment`, {
      email: TEST_EMAIL,
      subscriptionType: 'monthly'
    });
    console.log('✅ Monthly payment created');
    
    // Step 4: Check database for created records
    console.log('\n4️⃣ Verifying database records...');
    await delay(1000); // Give time for processing
    
    const paidUserResult = await pool.query(
      'SELECT * FROM paid_users WHERE email = $1 ORDER BY created_at DESC LIMIT 1',
      [TEST_EMAIL]
    );
    
    if (paidUserResult.rows.length === 0) {
      throw new Error('Paid user not created');
    }
    
    const paidUser = paidUserResult.rows[0];
    console.log('✅ Paid user created:');
    console.log('   - ID:', paidUser.id);
    console.log('   - Access Code:', paidUser.access_code);
    console.log('   - Type:', paidUser.subscription_type);
    console.log('   - Status:', paidUser.status);
    
    // Step 5: Check access code
    const accessCodeResult = await pool.query(
      'SELECT * FROM access_codes WHERE paid_user_id = $1',
      [paidUser.id]
    );
    
    if (accessCodeResult.rows.length === 0) {
      throw new Error('Access code not created');
    }
    
    console.log('✅ Access code created:', accessCodeResult.rows[0].code);
    
    // Step 6: Check email queue
    const emailResult = await pool.query(
      'SELECT * FROM email_queue WHERE to_email = $1 ORDER BY created_at DESC LIMIT 1',
      [TEST_EMAIL]
    );
    
    if (emailResult.rows.length > 0) {
      console.log('✅ Welcome email queued:');
      console.log('   - Template:', emailResult.rows[0].template);
      console.log('   - Status:', emailResult.rows[0].status);
    } else {
      console.log('⚠️  No email queued (SMTP might not be configured)');
    }
    
    // Step 7: Test yearly subscription with affiliate
    console.log('\n5️⃣ Creating yearly subscription payment...');
    const yearlyPayment = await axios.post(`${BASE_URL}/test/create-payment`, {
      email: '<EMAIL>',
      subscriptionType: 'yearly'
    });
    console.log('✅ Yearly payment created');
    
    await delay(1000);
    
    const yearlyUserResult = await pool.query(
      'SELECT * FROM paid_users WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (yearlyUserResult.rows.length > 0) {
      const yearlyUser = yearlyUserResult.rows[0];
      console.log('✅ Yearly subscriber created:');
      console.log('   - Access Code:', yearlyUser.access_code);
      console.log('   - Is Affiliate:', yearlyUser.is_affiliate);
      console.log('   - Affiliate Code:', yearlyUser.affiliate_code);
    }
    
    // Step 8: Simulate access code activation
    console.log('\n6️⃣ Simulating access code activation...');
    console.log('   User would text: START', paidUser.access_code);
    
    // Update database to simulate activation
    await pool.query(
      'UPDATE access_codes SET used_by_phone = $1, used_at = NOW() WHERE code = $2',
      [TEST_PHONE, paidUser.access_code]
    );
    
    await pool.query(
      'UPDATE paid_users SET phone = $1 WHERE id = $2',
      [TEST_PHONE, paidUser.id]
    );
    
    console.log('✅ Access code activated for phone:', TEST_PHONE);
    
    // Step 9: Check webhook events
    const webhookResult = await pool.query(
      'SELECT event_type, processed FROM webhook_events WHERE test_mode = true ORDER BY created_at DESC LIMIT 5'
    );
    
    if (webhookResult.rows.length > 0) {
      console.log('\n7️⃣ Webhook events logged:');
      webhookResult.rows.forEach(event => {
        console.log(`   - ${event.event_type}: ${event.processed ? 'Processed' : 'Pending'}`);
      });
    }
    
    // Step 10: Final status check
    console.log('\n8️⃣ Final status check...');
    const finalStatus = await axios.get(`${BASE_URL}/test/status`);
    console.log('✅ Final stats:', finalStatus.data.stats);
    
    console.log('\n✨ Payment flow test completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Configure your FastSpring webhook to point to: https://yourdomain.com/webhook/fastspring');
    console.log('2. Set PAYMENT_TEST_MODE=false in production');
    console.log('3. Add your FastSpring webhook secret to .env');
    console.log('4. Users can activate with: START', paidUser.access_code);
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  } finally {
    await pool.end();
  }
}

// Run test
testPaymentFlow().catch(console.error);