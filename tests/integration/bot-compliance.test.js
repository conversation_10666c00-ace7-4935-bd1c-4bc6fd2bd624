#!/usr/bin/env node

/**
 * Bot Compliance Testing Script
 * Tests all data subject rights and compliance features
 */

require('dotenv').config();
const axios = require('axios').default;

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const TEST_PHONE = '+15551234567'; // Test phone number

class BotComplianceTester {
  
  async runAllTests() {
    console.log('🧪 Starting Bot Compliance Tests...\n');
    
    const results = {
      passed: 0,
      failed: 0,
      tests: []
    };

    // Test privacy policy accessibility
    await this.testPrivacyPolicy(results);
    
    // Test terms of service accessibility  
    await this.testTermsOfService(results);
    
    // Test compliance endpoints (if admin access enabled)
    if (process.env.NODE_ENV === 'development' || process.env.ADMIN_ACCESS === 'true') {
      await this.testComplianceEndpoints(results);
    }
    
    // Test database compliance structures
    await this.testDatabaseCompliance(results);
    
    // Print results
    this.printResults(results);
    
    return results;
  }

  async testPrivacyPolicy(results) {
    console.log('📋 Testing Privacy Policy...');
    
    try {
      // Test direct privacy policy URL
      const privacyResponse = await axios.get(`${BASE_URL}/privacy`);
      this.addResult(results, 'Privacy Policy Redirect', privacyResponse.status === 200, 
        'Privacy policy should redirect to legal page');
      
      // Test legal page direct access
      const legalResponse = await axios.get(`${BASE_URL}/legal/privacy-policy.html`);
      this.addResult(results, 'Privacy Policy Content', 
        legalResponse.status === 200 && legalResponse.data.includes('GDPR'),
        'Privacy policy should contain GDPR compliance content');
        
    } catch (error) {
      this.addResult(results, 'Privacy Policy Access', false, `Error: ${error.message}`);
    }
  }

  async testTermsOfService(results) {
    console.log('📜 Testing Terms of Service...');
    
    try {
      // Test terms redirect
      const termsResponse = await axios.get(`${BASE_URL}/terms`);
      this.addResult(results, 'Terms of Service Redirect', termsResponse.status === 200,
        'Terms should redirect to legal page');
      
      // Test legal page content
      const legalResponse = await axios.get(`${BASE_URL}/legal/terms-of-service.html`);
      this.addResult(results, 'Terms of Service Content',
        legalResponse.status === 200 && legalResponse.data.includes('LIMITATION OF LIABILITY'),
        'Terms should contain liability limitations');
        
    } catch (error) {
      this.addResult(results, 'Terms of Service Access', false, `Error: ${error.message}`);
    }
  }

  async testComplianceEndpoints(results) {
    console.log('📊 Testing Compliance Endpoints...');
    
    try {
      // Test compliance stats endpoint
      const statsResponse = await axios.get(`${BASE_URL}/admin/compliance/stats`);
      this.addResult(results, 'Compliance Stats Endpoint', 
        statsResponse.status === 200 && statsResponse.data.audit && statsResponse.data.retention,
        'Compliance stats should return audit and retention data');
      
      // Test compliance report endpoint
      const reportResponse = await axios.get(`${BASE_URL}/admin/compliance/report`);
      this.addResult(results, 'Compliance Report Endpoint',
        reportResponse.status === 200 && reportResponse.data.reportPeriod,
        'Compliance report should return report period data');
      
      // Test retention compliance check
      const checkResponse = await axios.get(`${BASE_URL}/admin/compliance/check`);
      this.addResult(results, 'Retention Compliance Check',
        checkResponse.status === 200 && typeof checkResponse.data.compliant === 'boolean',
        'Retention compliance check should return compliance status');
        
    } catch (error) {
      this.addResult(results, 'Compliance Endpoints', false, `Error: ${error.message}`);
    }
  }

  async testDatabaseCompliance(results) {
    console.log('🗄️ Testing Database Compliance...');
    
    const pool = require('./src/db/connection');
    
    try {
      // Test that compliance tables exist
      const tables = [
        'user_consents',
        'data_requests', 
        'data_exports',
        'legal_documents',
        'audit_log'
      ];
      
      for (const table of tables) {
        try {
          const result = await pool.query(`SELECT COUNT(*) FROM ${table} LIMIT 1`);
          this.addResult(results, `Database Table: ${table}`, true,
            `${table} table exists and is accessible`);
        } catch (error) {
          this.addResult(results, `Database Table: ${table}`, false,
            `${table} table missing or inaccessible: ${error.message}`);
        }
      }
      
      // Test that users table has compliance fields
      const userColumns = await pool.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'users' AND table_schema = 'public'
      `);
      
      const requiredColumns = [
        'consent_given',
        'age_verified', 
        'terms_accepted',
        'opt_out_communications',
        'account_deletion_requested',
        'data_retention_date'
      ];
      
      const existingColumns = userColumns.rows.map(row => row.column_name);
      
      requiredColumns.forEach(column => {
        this.addResult(results, `User Table Column: ${column}`,
          existingColumns.includes(column),
          `Users table should have ${column} field for compliance`);
      });
      
      // Test compliance functions exist
      const functions = [
        'set_data_retention_date',
        'log_consent_change',
        'cleanup_expired_data'
      ];
      
      for (const func of functions) {
        try {
          const result = await pool.query(`
            SELECT proname FROM pg_proc WHERE proname = $1
          `, [func]);
          
          this.addResult(results, `Database Function: ${func}`,
            result.rows.length > 0,
            `${func} function should exist for compliance automation`);
        } catch (error) {
          this.addResult(results, `Database Function: ${func}`, false,
            `Error checking ${func}: ${error.message}`);
        }
      }
      
    } catch (error) {
      this.addResult(results, 'Database Compliance', false, 
        `Database connection error: ${error.message}`);
    }
  }

  addResult(results, testName, passed, message) {
    results.tests.push({
      name: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    });
    
    if (passed) {
      results.passed++;
      console.log(`✅ ${testName}: PASS`);
    } else {
      results.failed++;
      console.log(`❌ ${testName}: FAIL - ${message}`);
    }
  }

  printResults(results) {
    console.log('\n' + '='.repeat(60));
    console.log('🧪 BOT COMPLIANCE TEST RESULTS');
    console.log('='.repeat(60));
    console.log(`✅ Passed: ${results.passed}`);
    console.log(`❌ Failed: ${results.failed}`);
    console.log(`📊 Total: ${results.tests.length}`);
    console.log(`📈 Success Rate: ${((results.passed / results.tests.length) * 100).toFixed(1)}%`);
    
    if (results.failed > 0) {
      console.log('\n❌ Failed Tests:');
      results.tests
        .filter(test => !test.passed)
        .forEach(test => {
          console.log(`   • ${test.name}: ${test.message}`);
        });
    }
    
    console.log('\n📋 Compliance Status:');
    console.log(`   Legal Documents: ${results.tests.filter(t => 
      t.name.includes('Privacy Policy') || t.name.includes('Terms')).every(t => t.passed) ? '✅' : '❌'}`);
    console.log(`   Database Structure: ${results.tests.filter(t => 
      t.name.includes('Database')).every(t => t.passed) ? '✅' : '❌'}`);
    console.log(`   Admin Endpoints: ${results.tests.filter(t => 
      t.name.includes('Compliance')).every(t => t.passed) ? '✅' : '❌'}`);
    
    if (results.passed === results.tests.length) {
      console.log('\n🎉 ALL COMPLIANCE TESTS PASSED!');
      console.log('Bot is ready for compliant operation.');
    } else {
      console.log('\n⚠️  SOME TESTS FAILED');
      console.log('Please fix failing tests before deployment.');
    }
    
    console.log('='.repeat(60));
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new BotComplianceTester();
  tester.runAllTests()
    .then(results => {
      process.exit(results.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('Test runner failed:', error);
      process.exit(1);
    });
}

module.exports = BotComplianceTester;