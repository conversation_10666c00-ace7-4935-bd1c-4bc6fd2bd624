#!/usr/bin/env node

require('dotenv').config();
const emailService = require('./src/services/emailService');

async function testActualCustomerEmail() {
  try {
    console.log('🧪 TESTING ACTUAL CUSTOMER EMAIL DATA');
    console.log('='.repeat(60));
    
    // Use the exact template data from the most recent customer email
    const templateData = {
      "bumps": [],
      "botPhone": "+19035155547",
      "hasBumps": false,
      "accessCode": "HABIT-3AA7F5",
      "amountPaid": "39.99",
      "isLifetime": false,
      "accessCodes": [
        {
          "code": "HABIT-3AA7F5",
          "type": "primary",
          "description": "Main access code"
        }
      ],
      "bonus_codes": null,
      "isAffiliate": true,
      "pricingText": "$39.99/year",
      "primaryCode": "HABIT-3AA7F5",
      "affiliateCode": "AFF-F25974",
      "subscriptionType": "yearly",
      "subscription_type": "Annual",
      "subscription_price": "$39.99/year",
      "primary_access_code": "HABIT-3AA7F5",
      "subscriptionTypeDisplay": "Annual"
    };
    
    console.log('🎯 Using welcome_dynamic template with real customer data...');
    const emailContent = emailService.getWelcomeDynamicTemplate(templateData);
    
    if (!emailContent || !emailContent.html || !emailContent.text) {
      console.log('❌ Email content generation failed');
      return;
    }
    
    const htmlContent = emailContent.html;
    const textContent = emailContent.text;
    
    console.log('\n📊 EMAIL CONTENT ANALYSIS:');
    console.log(`   HTML Length: ${htmlContent.length} characters`);
    console.log(`   Text Length: ${textContent.length} characters`);
    
    // Check for Quick Setup section specifically
    console.log('\n🔍 QUICK SETUP SECTION ANALYSIS:');
    
    const quickSetupStart = htmlContent.indexOf('Quick Setup');
    const step5Text = 'Start building unstoppable habits!';
    const step5Index = htmlContent.indexOf(step5Text);
    
    if (quickSetupStart === -1) {
      console.log('   ❌ Quick Setup section not found');
    } else {
      console.log(`   ✅ Quick Setup section starts at position: ${quickSetupStart}`);
    }
    
    if (step5Index === -1) {
      console.log('   ❌ Step 5 text not found');
    } else {
      console.log(`   ✅ Step 5 text found at position: ${step5Index}`);
      
      const afterStep5 = htmlContent.substring(step5Index + step5Text.length);
      console.log(`   📊 Characters after step 5: ${afterStep5.length}`);
      
      // Check what comes immediately after step 5
      const next200chars = afterStep5.substring(0, 200);
      console.log('   📋 Next 200 characters after step 5:');
      console.log('   ' + '-'.repeat(50));
      console.log(next200chars.replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r'));
      console.log('   ' + '-'.repeat(50));
      
      // Check for footer content specifically
      const hasRichSignature = afterStep5.includes('Let\'s Lock In') && afterStep5.includes('Rich');
      const hasAffiliateSection = afterStep5.includes('💰 Earn with Lock In');
      const hasSocialLinks = afterStep5.includes('@richvieren');
      const hasCopyright = afterStep5.includes('© 2025 Lock In');
      
      console.log('\n   🔍 FOOTER CONTENT AFTER STEP 5:');
      console.log(`      Rich signature: ${hasRichSignature ? '✅' : '❌'}`);
      console.log(`      Affiliate section: ${hasAffiliateSection ? '✅' : '❌'}`);
      console.log(`      Social links: ${hasSocialLinks ? '✅' : '❌'}`);
      console.log(`      Copyright: ${hasCopyright ? '✅' : '❌'}`);
    }
    
    // Look for any truncation patterns
    console.log('\n🚨 TRUNCATION DETECTION:');
    const possibleTruncations = [
      { pattern: '</html>', name: 'HTML closing tag' },
      { pattern: '</body>', name: 'Body closing tag' },
      { pattern: '</div>', name: 'Div closing tag' },
      { pattern: '© 2025 Lock In. All rights reserved.', name: 'Full copyright' }
    ];
    
    possibleTruncations.forEach(({ pattern, name }) => {
      const found = htmlContent.includes(pattern);
      console.log(`   ${found ? '✅' : '❌'} ${name}: ${found ? 'Present' : 'MISSING'}`);
    });
    
    // Save the full HTML content to a file for inspection
    require('fs').writeFileSync('/var/www/lockin/debug-customer-email.html', htmlContent);
    console.log('\n💾 Full HTML content saved to: /var/www/lockin/debug-customer-email.html');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

testActualCustomerEmail().catch(console.error);