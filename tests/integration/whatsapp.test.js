#!/usr/bin/env node

/**
 * Simulate WhatsApp interactions to test the menu
 */

require('dotenv').config();
const stateMachine = require('./src/services/stateMachine');
const User = require('./src/models/User');
const pool = require('./src/db/connection');

async function simulateWhatsApp(phone, message) {
  console.log(`\n📱 USER SENDS: "${message}"`);
  console.log('─'.repeat(60));
  
  // Get user from database
  const user = await User.findByPhone(phone);
  if (!user) {
    console.log('❌ User not found!');
    return;
  }
  
  // Process message through state machine
  const response = await stateMachine.processMessage(user, message);
  
  console.log('🤖 BOT RESPONDS:');
  console.log(response.message);
  console.log('─'.repeat(60));
  
  return response;
}

async function runTest() {
  try {
    const testPhone = '+27646921984'; // <PERSON><PERSON>z's phone
    
    console.log('='.repeat(60));
    console.log('WHATSAPP SIMULATION TEST - RIZZ');
    console.log('='.repeat(60));
    
    // Test 1: Show main menu (blank slate)
    console.log('\n\n📋 TEST 1: BLANK SLATE (no habits logged)');
    await simulateWhatsApp(testPhone, 'menu');
    
    // Test 2: Log some habits (partial)
    console.log('\n\n📋 TEST 2: LOGGING PARTIAL HABITS');
    await simulateWhatsApp(testPhone, '1'); // Select "Log today's habits"
    await simulateWhatsApp(testPhone, '2,3'); // Log habits 2 and 3
    
    // Test 3: Back to menu (should show partial state)
    console.log('\n\n📋 TEST 3: MENU WITH PARTIAL LOGGING');
    await simulateWhatsApp(testPhone, 'menu');
    
    // Test 4: Complete logging
    console.log('\n\n📋 TEST 4: COMPLETE REMAINING HABITS');
    await simulateWhatsApp(testPhone, '1'); // Select "Complete today's habits"
    await simulateWhatsApp(testPhone, '1,4,5'); // Log habits 1, 4, 5 (1 and 4 as not done, 5 as done)
    
    // Test 5: Back to menu (should show fully logged)
    console.log('\n\n📋 TEST 5: MENU WITH FULL LOGGING');
    await simulateWhatsApp(testPhone, 'menu');
    
    // Show final database state
    console.log('\n\n📊 FINAL DATABASE STATE:');
    console.log('─'.repeat(60));
    const client = await pool.connect();
    const result = await client.query(`
      SELECT h.habit_number, h.habit_name, hl.completed
      FROM habits h
      LEFT JOIN habit_logs hl ON h.id = hl.habit_id AND hl.log_date = CURRENT_DATE
      WHERE h.user_id = (SELECT id FROM users WHERE phone = $1)
      ORDER BY h.habit_number
    `, [testPhone]);
    
    result.rows.forEach(row => {
      const status = row.completed === null ? '⚠️' : 
                     row.completed === true ? '✅' : '❌';
      console.log(`${status} ${row.habit_number}. ${row.habit_name}`);
    });
    
    client.release();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await pool.end();
  }
}

// Run the test
runTest().catch(console.error);