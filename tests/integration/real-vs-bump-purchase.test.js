#!/usr/bin/env node

require('dotenv').config();

// Same SMTP interception as before
const nodemailer = require('nodemailer');
const originalCreateTransport = nodemailer.createTransport;

let interceptedEmails = [];

nodemailer.createTransport = function(options) {
  const transporter = originalCreateTransport.call(this, options);
  const originalSendMail = transporter.sendMail;
  
  transporter.sendMail = async function(mailOptions) {
    console.log(`\n📧 INTERCEPTED EMAIL: ${mailOptions.to}`);
    console.log('Subject:', mailOptions.subject);
    console.log('HTML length:', mailOptions.html?.length || 0);
    
    const emailAnalysis = {
      to: mailOptions.to,
      htmlLength: mailOptions.html?.length || 0,
      hasCompleteFooter: false,
      charactersAfterStep5: 0
    };
    
    if (mailOptions.html) {
      const step5Index = mailOptions.html.indexOf('Start building unstoppable habits!');
      if (step5Index !== -1) {
        const afterStep5 = mailOptions.html.substring(step5Index + 'Start building unstoppable habits!'.length);
        emailAnalysis.charactersAfterStep5 = afterStep5.length;
        
        const footerChecks = [
          afterStep5.includes("Let's Lock In") && afterStep5.includes('Rich'),
          afterStep5.includes('💰 Earn with Lock In'),
          afterStep5.includes('@richvieren'),
          afterStep5.includes('© 2025 Lock In')
        ];
        
        emailAnalysis.hasCompleteFooter = footerChecks.every(check => check);
        
        console.log('Characters after step 5:', afterStep5.length);
        console.log('Complete footer:', emailAnalysis.hasCompleteFooter ? '✅' : '❌');
        
        // Save the email
        const type = mailOptions.to.includes('regular') ? 'regular' : 'bump';
        require('fs').writeFileSync(`/var/www/lockin/intercepted-${type}-purchase.html`, mailOptions.html);
      }
    }
    
    interceptedEmails.push(emailAnalysis);
    return originalSendMail.call(this, mailOptions);
  };
  
  return transporter;
};

async function testRealVsBumpPurchase() {
  try {
    console.log('🔍 TESTING REAL vs BUMP PURCHASE EMAILS...');
    
    const thrivecartController = require('./src/controllers/thrivecartController');
    
    console.log('\n1️⃣ SIMULATING REGULAR PURCHASE (NO BUMPS)');
    console.log('='.repeat(50));
    
    // Regular annual purchase - no bumps
    const regularWebhook = {
      thrivecart_account: 'richvieren',
      event: 'order.success',
      event_id: `REGULAR_${Date.now()}`,
      
      customer_email: '<EMAIL>',
      customer_name: 'Regular Customer',
      customer_first_name: 'Regular',
      customer_last_name: 'Customer',
      
      // Single annual product - no bumps
      product_name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
      order_total: '3999',
      payment_plan_name: 'Annual subscription (ongoing) ($39.99)',
      
      webhook_charges: [{
        name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
        amount: '3999',
        payment_plan_name: 'Annual subscription (ongoing) ($39.99)'
      }],
      
      thrivecart_secret: 'FUQ2A97V0Q8A'
    };
    
    const req1 = { body: regularWebhook };
    const res1 = { status: (code) => ({ json: (data) => res1 }) };
    
    await thrivecartController.handleWebhook(req1, res1);
    
    console.log('\n2️⃣ SIMULATING BUMP PURCHASE (WITH BUMPS)');
    console.log('='.repeat(50));
    
    // Purchase with bump offers
    const bumpWebhook = {
      thrivecart_account: 'richvieren',
      event: 'order.success',
      event_id: `BUMP_${Date.now()}`,
      
      customer_email: '<EMAIL>',
      customer_name: 'Bump Customer',
      customer_first_name: 'Bump',
      customer_last_name: 'Customer',
      
      // Main product + bump offers
      product_name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
      order_total: '11997', // Main + bumps
      payment_plan_name: 'Annual subscription (ongoing) ($39.99)',
      
      // Bump detection needs order.charges format
      order: {
        total_str: '119.97',
        charges: [
          {
            name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
            amount: '3999',
            payment_plan_name: 'Annual subscription (ongoing) ($39.99)',
            item_type: 'product'
          },
          {
            name: 'Add a Friend - 1 Extra Access Code',
            amount: '2000', // $20 in cents
            item_type: 'bump'
          },
          {
            name: 'Build a Tribe - 3 Extra Access Codes',
            amount: '6000', // $60 in cents  
            item_type: 'bump'
          }
        ]
      },
      
      thrivecart_secret: 'FUQ2A97V0Q8A'
    };
    
    const req2 = { body: bumpWebhook };
    const res2 = { status: (code) => ({ json: (data) => res2 }) };
    
    await thrivecartController.handleWebhook(req2, res2);
    
    console.log('\n📊 COMPARISON RESULTS:');
    console.log('='.repeat(50));
    
    if (interceptedEmails.length >= 2) {
      const regularEmail = interceptedEmails.find(e => e.to.includes('regular'));
      const bumpEmail = interceptedEmails.find(e => e.to.includes('bump'));
      
      if (regularEmail && bumpEmail) {
        console.log('\nREGULAR PURCHASE (BROKEN):');
        console.log('  HTML length:', regularEmail.htmlLength);
        console.log('  Chars after step 5:', regularEmail.charactersAfterStep5);
        console.log('  Complete footer:', regularEmail.hasCompleteFooter ? '✅' : '❌');
        
        console.log('\nBUMP PURCHASE (WORKING):');
        console.log('  HTML length:', bumpEmail.htmlLength);
        console.log('  Chars after step 5:', bumpEmail.charactersAfterStep5);
        console.log('  Complete footer:', bumpEmail.hasCompleteFooter ? '✅' : '❌');
        
        console.log('\n🎯 KEY FINDINGS:');
        if (regularEmail.hasCompleteFooter && bumpEmail.hasCompleteFooter) {
          console.log('❓ Both emails have complete footers at SMTP level!');
          console.log('❓ This suggests the issue is after SMTP (email provider/client)');
        } else if (!regularEmail.hasCompleteFooter && bumpEmail.hasCompleteFooter) {
          console.log('🚨 FOUND IT! Regular emails are truncated at SMTP level!');
          console.log('🚨 Bump emails have complete footers!');
          console.log('🔍 The bug is in the welcome_dynamic template processing!');
        } else {
          console.log('❓ Unexpected pattern - need to investigate further');
        }
      }
    }
    
    console.log('\n💾 Saved intercepted emails for comparison');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

testRealVsBumpPurchase().catch(console.error);