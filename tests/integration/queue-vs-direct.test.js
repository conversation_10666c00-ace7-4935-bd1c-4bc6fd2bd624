#!/usr/bin/env node

require('dotenv').config();
const nodemailer = require('nodemailer');
const pool = require('./src/db/connection');
const emailService = require('./src/services/emailService');

async function testQueueVsDirect() {
  try {
    console.log('🔍 TESTING QUEUE VS DIRECT SENDING...');
    
    // Get the most recent production email from queue
    const result = await pool.query("SELECT * FROM email_queue WHERE to_email = '<EMAIL>' ORDER BY id DESC LIMIT 1");
    const emailRecord = result.rows[0];
    
    console.log('Using email record ID:', emailRecord.id);
    console.log('Template:', emailRecord.template);
    
    // Create transporter
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_PORT === '465',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
    
    console.log('\n1️⃣ SIMULATING EXACT QUEUE PROCESSING:');
    
    // Get template exactly like queue does
    const templateFunc = emailService.templates[emailRecord.template];
    if (!templateFunc) {
      throw new Error(`Unknown email template: ${emailRecord.template}`);
    }

    // Generate content exactly like queue does
    const templateData = typeof emailRecord.template_data === 'string' 
      ? JSON.parse(emailRecord.template_data) 
      : emailRecord.template_data;
    const { subject, html, text } = templateFunc(templateData);
    
    console.log('Queue-style generation:');
    console.log('  HTML length:', html.length);
    console.log('  Text length:', text.length);
    
    // Send exactly like queue does
    const queueMailOptions = {
      from: `"Lockin Habit Tracker" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: '<EMAIL>',
      subject: emailRecord.subject || subject,
      html,
      text,
      replyTo: process.env.EMAIL_REPLY_TO || '<EMAIL>'
    };
    
    console.log('Sending queue-style email...');
    const queueResult = await transporter.sendMail(queueMailOptions);
    console.log('Queue email sent:', queueResult.messageId);
    
    console.log('\n2️⃣ SIMULATING DIRECT SENDING (working method):');
    
    // Generate content like direct method does
    const { subject: subject2, html: html2, text: text2 } = emailService.getWelcomeDynamicTemplate(templateData);
    
    console.log('Direct-style generation:');
    console.log('  HTML length:', html2.length);
    console.log('  Text length:', text2.length);
    
    // Send like direct method does
    const directMailOptions = {
      from: `"DEBUG Direct" <${process.env.EMAIL_FROM}>`,
      to: '<EMAIL>',
      subject: 'DEBUG - Direct Style Email',
      html: html2,
      text: text2
    };
    
    console.log('Sending direct-style email...');
    const directResult = await transporter.sendMail(directMailOptions);
    console.log('Direct email sent:', directResult.messageId);
    
    console.log('\n📊 COMPARISON:');
    console.log('Same HTML length?', html.length === html2.length);
    console.log('Same text length?', text.length === text2.length);
    console.log('Same HTML content?', html === html2);
    
    if (html !== html2) {
      console.log('🚨 DIFFERENT HTML CONTENT FOUND!');
      console.log('Queue HTML length:', html.length);
      console.log('Direct HTML length:', html2.length);
      
      // Save both for comparison
      require('fs').writeFileSync('/var/www/lockin/queue-style-email.html', html);
      require('fs').writeFileSync('/var/www/lockin/direct-style-email.html', html2);
      console.log('💾 Saved both versions for detailed comparison');
    } else {
      console.log('✅ Same content - issue must be elsewhere');
    }
    
    console.log('\n🎯 CHECK BOTH EMAILS:');
    console.log('1. Queue-style: Subject starts with "🚀 Your LOCK IN Access Code"');
    console.log('2. Direct-style: Subject "DEBUG - Direct Style Email"');
    console.log('Compare which one has complete footer content');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
  
  await pool.end();
}

testQueueVsDirect().catch(console.error);