#!/usr/bin/env node

require('dotenv').config();
const emailService = require('./src/services/emailService');

async function testAllSubscriptionFooters() {
  try {
    console.log('🧪 TESTING ALL SUBSCRIPTION FOOTER CONTENT');
    console.log('='.repeat(60));
    
    // Test data for different subscription types
    const testData = {
      monthly: {
        email: '<EMAIL>',
        firstName: 'Monthly',
        lastName: 'Tester',
        productType: 'habit-tracker-monthly',
        subscriptionType: 'monthly',
        accessCode: 'MONTHLY123'
      },
      yearly: {
        email: '<EMAIL>',
        firstName: 'Yearly',
        lastName: 'Tester',
        productType: 'habit-tracker-yearly',
        subscriptionType: 'yearly',
        accessCode: 'YEARLY456'
      },
      lifetime: {
        email: '<EMAIL>',
        firstName: 'Lifetime',
        lastName: 'Tester',
        productType: 'habit-tracker-lifetime',
        subscriptionType: 'lifetime',
        accessCode: 'LIFETIME789'
      }
    };

    // Test each subscription type
    for (const [type, data] of Object.entries(testData)) {
      console.log(`\n📧 TESTING ${type.toUpperCase()} SUBSCRIPTION`);
      console.log('─'.repeat(50));
      
      try {
        // Generate email content using the service templates directly
        let emailContent;
        
        if (type === 'monthly') {
          emailContent = emailService.getWelcomeMonthlyTemplate(data);
        } else if (type === 'yearly') {
          emailContent = emailService.getWelcomeYearlyTemplate(data);
        } else if (type === 'lifetime') {
          // Use dynamic template for lifetime (single code)
          emailContent = emailService.getWelcomeDynamicTemplate(data);
        }
        
        // Check if content exists
        if (!emailContent || !emailContent.html || !emailContent.text) {
          console.log(`   ❌ ${type}: Email content generation failed`);
          continue;
        }
        
        const htmlContent = emailContent.html;
        const textContent = emailContent.text;
        
        // Check for footer content in HTML
        const htmlChecks = {
          'Rich signature': htmlContent.includes('Let\'s Lock In,<br>') && htmlContent.includes('Rich'),
          'Social links': htmlContent.includes('@richvieren') && htmlContent.includes('@vieren'),
          'Affiliate section': htmlContent.includes('💰 Earn with Lock In') && htmlContent.includes('25% recurring commission'),
          'Affiliate link': htmlContent.includes('https://aeon.thrivecart.com/lock-in-annual/partner/'),
          'Website link': htmlContent.includes('www.lockintracker.com'),
          'Instagram link': htmlContent.includes('@lockintracker'),
          'Copyright': htmlContent.includes('© 2025 Lock In. All rights reserved.')
        };
        
        // Check for footer content in text
        const textChecks = {
          'Rich signature': textContent.includes('Let\'s Lock In,\nRich'),
          'Social links': textContent.includes('@richvieren') && textContent.includes('@vieren'),
          'Affiliate section': textContent.includes('💰 EARN WITH LOCK IN') && textContent.includes('25% recurring commission'),
          'Affiliate link': textContent.includes('https://aeon.thrivecart.com/lock-in-annual/partner/'),
          'Website link': textContent.includes('www.lockintracker.com'),
          'Instagram link': textContent.includes('@lockintracker'),
          'Copyright': textContent.includes('© 2025 Lock In. All rights reserved.')
        };
        
        // Check for template-specific content and footer presence
        let quickSetupCheck = {};
        if (type === 'lifetime') {
          // For lifetime (dynamic template), check for Quick Setup truncation
          quickSetupCheck = {
            'Step 5 present': htmlContent.includes('Start building unstoppable habits!'),
            'Content after step 5': htmlContent.split('Start building unstoppable habits!').length > 1,
            'Footer after step 5': htmlContent.split('Start building unstoppable habits!')[1] && 
                                   htmlContent.split('Start building unstoppable habits!')[1].includes('Let\'s Lock In')
          };
        } else {
          // For monthly/yearly templates, just check for footer presence after last step
          quickSetupCheck = {
            'Last step present': htmlContent.includes('5. Start tracking your daily progress!'),
            'Footer present': htmlContent.includes('Let\'s Lock In'),
            'Complete template': htmlContent.includes('© 2025 Lock In. All rights reserved.')
          };
        }
        
        console.log(`   📋 HTML Footer Check:`);
        Object.entries(htmlChecks).forEach(([check, passed]) => {
          console.log(`      ${passed ? '✅' : '❌'} ${check}`);
        });
        
        console.log(`   📋 Text Footer Check:`);
        Object.entries(textChecks).forEach(([check, passed]) => {
          console.log(`      ${passed ? '✅' : '❌'} ${check}`);
        });
        
        console.log(`   📋 Template Content Check:`);
        Object.entries(quickSetupCheck).forEach(([check, passed]) => {
          console.log(`      ${passed ? '✅' : '❌'} ${check}`);
        });
        
        // Count characters after key content
        let afterContent = '';
        if (type === 'lifetime') {
          afterContent = htmlContent.split('Start building unstoppable habits!')[1] || '';
          console.log(`   📊 Characters after step 5: ${afterContent.length}`);
        } else {
          afterContent = htmlContent.split('5. Start tracking your daily progress!')[1] || '';
          console.log(`   📊 Characters after last step: ${afterContent.length}`);
        }
        
        // Overall verdict
        const htmlPassed = Object.values(htmlChecks).every(v => v);
        const textPassed = Object.values(textChecks).every(v => v);
        const contentPassed = Object.values(quickSetupCheck).every(v => v);
        
        if (htmlPassed && textPassed && contentPassed) {
          console.log(`   🎉 ${type.toUpperCase()}: FOOTER WORKING CORRECTLY`);
        } else {
          console.log(`   ❌ ${type.toUpperCase()}: FOOTER ISSUES DETECTED`);
          if (!contentPassed && type === 'lifetime') {
            console.log(`   🚨 TRUNCATION ISSUE: Email cuts off after step 5`);
          } else if (!contentPassed) {
            console.log(`   🚨 TEMPLATE ISSUE: Missing footer content`);
          }
        }
        
      } catch (error) {
        console.log(`   ❌ ${type}: Error generating email - ${error.message}`);
      }
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('✅ FOOTER TESTING COMPLETE');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

testAllSubscriptionFooters().catch(console.error);