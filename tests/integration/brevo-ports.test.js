#!/usr/bin/env node

require('dotenv').config();
const net = require('net');

console.log('Testing connectivity to Brevo SMTP on different ports...\n');

const hosts = [
  { host: 'smtp-relay.brevo.com', port: 587, name: '<PERSON><PERSON> (STARTTLS)' },
  { host: 'smtp-relay.brevo.com', port: 465, name: 'SMTP (SSL/TLS)' },
  { host: 'smtp-relay.brevo.com', port: 25, name: 'SMTP (Plain)' },
  { host: 'smtp-relay.brevo.com', port: 2525, name: 'SMTP (Alternative)' }
];

async function testConnection(config) {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    
    socket.setTimeout(3000);
    
    socket.on('connect', () => {
      console.log(`✅ ${config.name} on port ${config.port}: CONNECTED`);
      socket.destroy();
      resolve(true);
    });
    
    socket.on('timeout', () => {
      console.log(`❌ ${config.name} on port ${config.port}: TIMEOUT`);
      socket.destroy();
      resolve(false);
    });
    
    socket.on('error', (err) => {
      console.log(`❌ ${config.name} on port ${config.port}: ${err.code || err.message}`);
      resolve(false);
    });
    
    socket.connect(config.port, config.host);
  });
}

async function testAll() {
  for (const config of hosts) {
    await testConnection(config);
  }
  
  console.log('\nTesting general internet connectivity...');
  await testConnection({ host: 'google.com', port: 443, name: 'Google HTTPS' });
  await testConnection({ host: '*******', port: 53, name: 'Google DNS' });
}

testAll();