const { validateWebhook, validatePhone, sanitizeInput } = require('../../src/middleware/validation');

describe('Validation Middleware', () => {
  describe('validateWebhook', () => {
    let req, res, next;

    beforeEach(() => {
      req = {
        body: {
          Body: 'Test message',
          From: '+**********',
          To: '+**********',
          MessageSid: 'MSG123',
          AccountSid: 'ACC123'
        },
        ip: '127.0.0.1'
      };
      res = {
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      };
      next = jest.fn();
    });

    it('should pass valid webhook request', () => {
      validateWebhook(req, res, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    it('should reject invalid phone format', () => {
      req.body.From = '**********'; // Missing + prefix

      validateWebhook(req, res, next);

      expect(next).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith('Invalid request');
    });

    it('should reject missing required fields', () => {
      delete req.body.MessageSid;

      validateWebhook(req, res, next);

      expect(next).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(400);
    });

    it('should sanitize message body', () => {
      req.body.Body = '  <script>alert("xss")</script>Test  ';

      validateWebhook(req, res, next);

      expect(req.body.Body).toBe('Test');
      expect(next).toHaveBeenCalled();
    });
  });

  describe('validatePhone', () => {
    it('should validate correct E.164 format', () => {
      expect(validatePhone('+**********')).toBe(true);
      expect(validatePhone('+442071234567')).toBe(true);
      expect(validatePhone('+919876543210')).toBe(true);
    });

    it('should reject invalid formats', () => {
      expect(validatePhone('**********')).toBe(false);
      expect(validatePhone('(*************')).toBe(false);
      expect(validatePhone('+0123456789')).toBe(false); // Can't start with 0
      expect(validatePhone('phone')).toBe(false);
    });
  });

  describe('sanitizeInput', () => {
    it('should remove SQL injection attempts', () => {
      expect(sanitizeInput('DROP TABLE users')).toBe('');
      expect(sanitizeInput('DELETE FROM habits')).toBe('');
      expect(sanitizeInput('SELECT * FROM users')).toBe('');
    });

    it('should allow normal text with SQL keywords', () => {
      expect(sanitizeInput('I need to select a habit')).toBe('I need to select a habit');
      expect(sanitizeInput('Please update my name')).toBe('Please update my name');
    });

    it('should remove HTML tags', () => {
      expect(sanitizeInput('<b>Bold</b> text')).toBe('Bold text');
      expect(sanitizeInput('<script>alert("xss")</script>')).toBe('');
    });

    it('should trim and limit length', () => {
      const longText = 'a'.repeat(2000);
      const result = sanitizeInput(longText);
      
      expect(result.length).toBe(1000);
    });

    it('should handle empty input', () => {
      expect(sanitizeInput('')).toBe('');
      expect(sanitizeInput(null)).toBe('');
      expect(sanitizeInput(undefined)).toBe('');
    });
  });
});