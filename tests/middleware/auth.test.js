/**
 * Unit tests for Authentication Middleware
 */

const AuthMiddleware = require('../../src/middleware/auth');
const jwt = require('jsonwebtoken');
const AuditLog = require('../../src/models/AuditLog');

jest.mock('../../src/models/AuditLog');
jest.mock('../../src/config/logger');

describe('AuthMiddleware', () => {
  let req, res, next;

  beforeEach(() => {
    jest.clearAllMocks();
    
    req = {
      headers: {},
      query: {},
      ip: '127.0.0.1',
      path: '/admin/test',
      method: 'GET',
      body: {},
      get: jest.fn().mockReturnValue('test-user-agent')
    };
    
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };
    
    next = jest.fn();
    
    AuditLog.log = jest.fn().mockResolvedValue(true);
    
    // Set test JWT secret
    process.env.JWT_SECRET = 'test-secret';
  });

  describe('generateToken', () => {
    test('should generate a valid JWT token', () => {
      const payload = { userId: 1, role: 'admin' };
      const token = AuthMiddleware.generateToken(payload);
      
      expect(token).toBeDefined();
      
      const decoded = jwt.verify(token, 'test-secret');
      expect(decoded.userId).toBe(1);
      expect(decoded.role).toBe('admin');
    });

    test('should accept custom options', () => {
      const payload = { userId: 1, role: 'user' };
      const token = AuthMiddleware.generateToken(payload, { expiresIn: '1h' });
      
      const decoded = jwt.verify(token, 'test-secret');
      expect(decoded).toBeDefined();
    });
  });

  describe('verifyToken', () => {
    test('should verify a valid token', () => {
      const token = jwt.sign({ userId: 1, role: 'admin' }, 'test-secret');
      const decoded = AuthMiddleware.verifyToken(token);
      
      expect(decoded.userId).toBe(1);
      expect(decoded.role).toBe('admin');
    });

    test('should throw error for invalid token', () => {
      expect(() => AuthMiddleware.verifyToken('invalid-token')).toThrow('Invalid token');
    });

    test('should throw error for expired token', () => {
      const token = jwt.sign(
        { userId: 1, role: 'admin' },
        'test-secret',
        { expiresIn: '-1h' }
      );
      
      expect(() => AuthMiddleware.verifyToken(token)).toThrow('Invalid token');
    });
  });

  describe('authenticateAdmin', () => {
    test('should authenticate valid admin token', async () => {
      const token = jwt.sign({ userId: 1, role: 'admin' }, 'test-secret');
      req.headers.authorization = `Bearer ${token}`;
      
      await AuthMiddleware.authenticateAdmin(req, res, next);
      
      expect(req.user).toBeDefined();
      expect(req.user.userId).toBe(1);
      expect(req.user.role).toBe('admin');
      expect(next).toHaveBeenCalled();
      expect(AuditLog.log).toHaveBeenCalled();
    });

    test('should reject non-admin token', async () => {
      const token = jwt.sign({ userId: 1, role: 'user' }, 'test-secret');
      req.headers.authorization = `Bearer ${token}`;
      
      await AuthMiddleware.authenticateAdmin(req, res, next);
      
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Insufficient permissions'
      }));
      expect(next).not.toHaveBeenCalled();
    });

    test('should reject missing authorization header', async () => {
      await AuthMiddleware.authenticateAdmin(req, res, next);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Authentication required'
      }));
      expect(next).not.toHaveBeenCalled();
    });

    test('should fall back to API key authentication', async () => {
      process.env.ADMIN_API_KEYS = 'test-api-key,another-key';
      req.headers['x-api-key'] = 'test-api-key';
      
      await AuthMiddleware.authenticateAdmin(req, res, next);
      
      expect(req.user).toBeDefined();
      expect(req.user.role).toBe('admin');
      expect(req.user.authMethod).toBe('api-key');
      expect(next).toHaveBeenCalled();
    });

    test('should reject invalid API key', async () => {
      process.env.ADMIN_API_KEYS = 'test-api-key';
      req.headers['x-api-key'] = 'wrong-key';
      
      await AuthMiddleware.authenticateAdmin(req, res, next);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Invalid API key'
      }));
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('authenticateUser', () => {
    test('should authenticate valid user token', async () => {
      const token = jwt.sign({ userId: 1, role: 'user' }, 'test-secret');
      req.headers.authorization = `Bearer ${token}`;
      
      await AuthMiddleware.authenticateUser(req, res, next);
      
      expect(req.user).toBeDefined();
      expect(req.user.userId).toBe(1);
      expect(next).toHaveBeenCalled();
    });

    test('should reject missing authorization header', async () => {
      await AuthMiddleware.authenticateUser(req, res, next);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('handleAdminLogin', () => {
    beforeEach(() => {
      process.env.ADMIN_USERNAME = 'admin';
      process.env.ADMIN_PASSWORD = 'secure-password';
    });

    test('should generate token for valid credentials', async () => {
      req.body = { username: 'admin', password: 'secure-password' };
      
      await AuthMiddleware.handleAdminLogin(req, res);
      
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        token: expect.any(String),
        expiresIn: '24h',
        message: 'Login successful'
      }));
      expect(AuditLog.log).toHaveBeenCalled();
    });

    test('should reject invalid username', async () => {
      req.body = { username: 'wrong', password: 'secure-password' };
      
      await AuthMiddleware.handleAdminLogin(req, res);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Invalid credentials'
      }));
    });

    test('should reject invalid password', async () => {
      req.body = { username: 'admin', password: 'wrong-password' };
      
      await AuthMiddleware.handleAdminLogin(req, res);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Invalid credentials'
      }));
    });
  });

  describe('authenticateApiKey', () => {
    beforeEach(() => {
      process.env.ADMIN_API_KEYS = 'key1,key2,key3';
    });

    test('should authenticate valid API key in header', async () => {
      req.headers['x-api-key'] = 'key2';
      
      await AuthMiddleware.authenticateApiKey(req, res, next);
      
      expect(req.user).toBeDefined();
      expect(req.user.authMethod).toBe('api-key');
      expect(next).toHaveBeenCalled();
    });

    test('should authenticate valid API key in query', async () => {
      req.query.api_key = 'key3';
      
      await AuthMiddleware.authenticateApiKey(req, res, next);
      
      expect(req.user).toBeDefined();
      expect(next).toHaveBeenCalled();
    });

    test('should reject invalid API key', async () => {
      req.headers['x-api-key'] = 'invalid-key';
      
      await AuthMiddleware.authenticateApiKey(req, res, next);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(next).not.toHaveBeenCalled();
    });

    test('should handle empty API keys list', async () => {
      process.env.ADMIN_API_KEYS = '';
      req.headers['x-api-key'] = 'any-key';
      
      await AuthMiddleware.authenticateApiKey(req, res, next);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(next).not.toHaveBeenCalled();
    });
  });
});