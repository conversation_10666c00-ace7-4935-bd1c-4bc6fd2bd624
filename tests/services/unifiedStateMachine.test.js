/**
 * Unit tests for UnifiedStateMachine service
 */

const unifiedStateMachine = require('../../src/services/unifiedStateMachine');
const User = require('../../src/models/User');
const Habit = require('../../src/models/Habit');
const { STATES, USER_STATUS } = require('../../src/config/constants');

// Mock dependencies
jest.mock('../../src/models/User');
jest.mock('../../src/models/Habit');
jest.mock('../../src/models/AuditLog');
jest.mock('../../src/config/logger');

describe('UnifiedStateMachine', () => {
  let mockUser;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup mock user
    mockUser = {
      id: 1,
      phone: '+1234567890',
      display_name: 'Test User',
      status: USER_STATUS.ACTIVE,
      current_state: STATES.MAIN_MENU,
      timezone: 'America/New_York',
      is_unlocked: true,
      opted_out_at: null
    };

    // Mock User methods
    User.updateLastActive = jest.fn().mockResolvedValue(true);
    User.updateState = jest.fn().mockResolvedValue(true);
    User.updateDisplayName = jest.fn().mockResolvedValue(true);
    User.updateTimezone = jest.fn().mockResolvedValue(true);
    User.updateStatus = jest.fn().mockResolvedValue(true);
    User.normalizePhone = jest.fn().mockReturnValue('+1234567890');
  });

  describe('processMessage', () => {
    test('should process main menu selection correctly', async () => {
      const response = await unifiedStateMachine.processMessage(mockUser, '1');
      
      expect(User.updateLastActive).toHaveBeenCalledWith(1);
      expect(User.updateState).toHaveBeenCalled();
      expect(response).toHaveProperty('message');
      expect(response).toHaveProperty('media');
    });

    test('should handle invalid menu selection', async () => {
      const response = await unifiedStateMachine.processMessage(mockUser, 'invalid');
      
      expect(response.message).toContain('Please select a valid option');
    });

    test('should handle setting habits', async () => {
      mockUser.current_state = STATES.SETTING_HABIT;
      Habit.getByUserId = jest.fn().mockResolvedValue([]);
      Habit.create = jest.fn().mockResolvedValue(true);
      
      const response = await unifiedStateMachine.processMessage(mockUser, 'Exercise daily');
      
      expect(Habit.create).toHaveBeenCalledWith(1, 'Exercise daily');
      expect(response.message).toContain('has been added');
    });
  });

  describe('Strategy Selection', () => {
    test('should use correct strategy based on environment', () => {
      const strategyName = unifiedStateMachine.getStrategyName();
      expect(strategyName).toBeDefined();
      expect(['CoreStateMachineStrategy', 'PaymentEnforcementStrategy', 'ComplianceStrategy'])
        .toContain(strategyName);
    });

    test('should reload strategy with new configuration', () => {
      const initialStrategy = unifiedStateMachine.getStrategyName();
      
      unifiedStateMachine.reloadStrategy({
        enforceCompliance: false,
        enforcePayment: false
      });
      
      expect(unifiedStateMachine.getStrategyName()).toBe('CoreStateMachineStrategy');
      
      // Restore original
      unifiedStateMachine.reloadStrategy();
    });
  });

  describe('Payment Enforcement', () => {
    test('should block locked users from accessing paid features', async () => {
      mockUser.status = USER_STATUS.LOCKED;
      mockUser.is_unlocked = false;
      
      // Reload with payment enforcement
      unifiedStateMachine.reloadStrategy({ enforcePayment: true });
      
      const response = await unifiedStateMachine.processMessage(mockUser, '1');
      
      expect(response.message).toContain('ACCOUNT LOCKED');
    });

    test('should validate access codes', async () => {
      mockUser.status = USER_STATUS.LOCKED;
      
      const paymentService = require('../../src/services/paymentService');
      paymentService.validateAccessCode = jest.fn().mockResolvedValue(true);
      paymentService.unlockUser = jest.fn().mockResolvedValue(true);
      
      unifiedStateMachine.reloadStrategy({ enforcePayment: true });
      
      const response = await unifiedStateMachine.processMessage(mockUser, 'ABC123');
      
      expect(paymentService.validateAccessCode).toHaveBeenCalledWith('ABC123');
    });
  });

  describe('Compliance Features', () => {
    beforeEach(() => {
      // Reload with compliance enabled
      unifiedStateMachine.reloadStrategy({ enforceCompliance: true });
    });

    test('should handle STOP command', async () => {
      const complianceService = require('../../src/services/complianceService');
      complianceService.handleOptOut = jest.fn().mockResolvedValue(true);
      
      const response = await unifiedStateMachine.processMessage(mockUser, 'STOP');
      
      expect(response.message).toContain('unsubscribed');
    });

    test('should handle data deletion request', async () => {
      const userRightsService = require('../../src/services/userRightsService');
      User.updateSessionContext = jest.fn().mockResolvedValue(true);
      
      const response = await unifiedStateMachine.processMessage(mockUser, 'DELETE MY DATA');
      
      expect(response.message).toContain('DATA DELETION REQUEST');
      expect(User.updateSessionContext).toHaveBeenCalled();
    });

    test('should handle data export request', async () => {
      const userRightsService = require('../../src/services/userRightsService');
      userRightsService.exportUserData = jest.fn().mockResolvedValue({
        habits: [],
        logs: [],
        auditLogs: []
      });
      
      const response = await unifiedStateMachine.processMessage(mockUser, 'EXPORT MY DATA');
      
      expect(response.message).toContain('DATA EXPORT');
      expect(userRightsService.exportUserData).toHaveBeenCalledWith(1);
    });

    test('should enforce 24-hour message window', async () => {
      mockUser.last_active = new Date(Date.now() - 25 * 60 * 60 * 1000); // 25 hours ago
      
      const response = await unifiedStateMachine.processMessage(mockUser, 'test');
      
      expect(response.message).toContain('24-hour messaging window');
    });
  });

  describe('Error Handling', () => {
    test('should handle errors gracefully', async () => {
      User.updateLastActive = jest.fn().mockRejectedValue(new Error('Database error'));
      
      const response = await unifiedStateMachine.processMessage(mockUser, '1');
      
      expect(response.message).toContain('something went wrong');
    });
  });
});

// Mock the services that ComplianceStrategy uses
jest.mock('../../src/services/complianceService', () => ({
  handleOptOut: jest.fn().mockResolvedValue(true),
  handleOptIn: jest.fn().mockResolvedValue(true)
}));

jest.mock('../../src/services/userRightsService', () => ({
  exportUserData: jest.fn().mockResolvedValue({
    habits: [],
    logs: [],
    auditLogs: []
  }),
  deleteUserData: jest.fn().mockResolvedValue(true)
}));

jest.mock('../../src/services/paymentService', () => ({
  validateAccessCode: jest.fn().mockResolvedValue(false),
  unlockUser: jest.fn().mockResolvedValue(true)
}));