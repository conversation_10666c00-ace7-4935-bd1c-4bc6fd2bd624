version: '3.8'

services:
  app:
    build: .
    container_name: whatsapp-habit-tracker
    restart: unless-stopped
    ports:
      - "${PORT:-3000}:3000"
    environment:
      NODE_ENV: production
      PORT: 3000
      DATABASE_URL: ${DATABASE_URL}
      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
      TWILIO_PHONE_NUMBER: ${TWILIO_PHONE_NUMBER}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_DIR: /var/log
    volumes:
      - app-logs:/var/log
    networks:
      - app-network
    depends_on:
      - postgres

  postgres:
    image: postgres:15-alpine
    container_name: habit-tracker-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-habittracker}
      POSTGRES_USER: ${POSTGRES_USER:-habituser}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_HOST_AUTH_METHOD: md5
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./src/db/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
    ports:
      - "5432:5432"
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-habituser} -d ${POSTGRES_DB:-habittracker}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Optional: pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: habit-tracker-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    networks:
      - app-network
    depends_on:
      - postgres
    profiles:
      - debug

volumes:
  postgres-data:
  app-logs:

networks:
  app-network:
    driver: bridge